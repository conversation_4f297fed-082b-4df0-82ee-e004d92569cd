import type { _<PERSON><PERSON><PERSON><PERSON><PERSON>, _UndoRedoGridApi } from '../api/gridApi';
import type { _ModuleWithApi, _ModuleWithoutApi } from '../interfaces/iModule';
/**
 * @internal
 */
export declare const EditCoreModule: _ModuleWithApi<_EditGridApi<any>>;
/**
 * @feature Editing -> Undo / Redo Edits
 */
export declare const UndoRedoEditModule: _ModuleWithApi<_UndoRedoGridApi>;
/**
 * @feature Editing -> Text Editor
 */
export declare const TextEditorModule: _ModuleWithoutApi;
/**
 * @feature Editing -> Number Editor
 */
export declare const NumberEditorModule: _ModuleWithoutApi;
/**
 * @feature Editing -> Date Editor
 */
export declare const DateEditorModule: _ModuleWithoutApi;
/**
 * @feature Editing -> Checkbox Editor
 */
export declare const CheckboxEditorModule: _ModuleWithoutApi;
/**
 * @feature Editing -> Select Editor
 */
export declare const SelectEditorModule: _ModuleWithoutApi;
/**
 * @feature Editing -> Large Text Editor
 */
export declare const LargeTextEditorModule: _ModuleWithoutApi;
/**
 * @feature Editing
 */
export declare const CustomEditorModule: _ModuleWithoutApi;
