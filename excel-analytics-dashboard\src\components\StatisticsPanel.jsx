import React, { useMemo } from 'react';
import {
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Divider,
  LinearProgress
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Analytics,
  Calculate,
  Assessment,
  Functions
} from '@mui/icons-material';

const StatisticsPanel = ({ data, columns }) => {
  // Calculate comprehensive statistics
  const statistics = useMemo(() => {
    if (!data || data.length === 0) return {};

    const stats = {};
    
    columns.forEach(col => {
      const values = data.map(row => row[col.field]).filter(val => val !== null && val !== undefined && val !== '');
      const numericValues = values.map(val => parseFloat(val)).filter(val => !isNaN(val));
      
      if (numericValues.length > 0) {
        // Basic statistics
        const sum = numericValues.reduce((a, b) => a + b, 0);
        const mean = sum / numericValues.length;
        const sortedValues = [...numericValues].sort((a, b) => a - b);
        const median = sortedValues.length % 2 === 0 
          ? (sortedValues[sortedValues.length / 2 - 1] + sortedValues[sortedValues.length / 2]) / 2
          : sortedValues[Math.floor(sortedValues.length / 2)];
        
        // Variance and Standard Deviation
        const variance = numericValues.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / numericValues.length;
        const stdDev = Math.sqrt(variance);
        
        // Quartiles
        const q1Index = Math.floor(sortedValues.length * 0.25);
        const q3Index = Math.floor(sortedValues.length * 0.75);
        const q1 = sortedValues[q1Index];
        const q3 = sortedValues[q3Index];
        const iqr = q3 - q1;
        
        stats[col.field] = {
          columnName: col.headerName,
          count: numericValues.length,
          sum: sum,
          mean: mean,
          median: median,
          min: Math.min(...numericValues),
          max: Math.max(...numericValues),
          range: Math.max(...numericValues) - Math.min(...numericValues),
          variance: variance,
          stdDev: stdDev,
          q1: q1,
          q3: q3,
          iqr: iqr,
          skewness: calculateSkewness(numericValues, mean, stdDev),
          kurtosis: calculateKurtosis(numericValues, mean, stdDev)
        };
      } else {
        // For non-numeric columns
        const uniqueValues = [...new Set(values)];
        const valueCounts = {};
        values.forEach(val => {
          valueCounts[val] = (valueCounts[val] || 0) + 1;
        });
        
        stats[col.field] = {
          columnName: col.headerName,
          count: values.length,
          uniqueCount: uniqueValues.length,
          mostFrequent: Object.keys(valueCounts).reduce((a, b) => valueCounts[a] > valueCounts[b] ? a : b),
          valueCounts: valueCounts,
          isNumeric: false
        };
      }
    });
    
    return stats;
  }, [data, columns]);

  // Helper functions
  const calculateSkewness = (values, mean, stdDev) => {
    if (stdDev === 0) return 0;
    const n = values.length;
    const skew = values.reduce((acc, val) => acc + Math.pow((val - mean) / stdDev, 3), 0) / n;
    return skew;
  };

  const calculateKurtosis = (values, mean, stdDev) => {
    if (stdDev === 0) return 0;
    const n = values.length;
    const kurt = values.reduce((acc, val) => acc + Math.pow((val - mean) / stdDev, 4), 0) / n - 3;
    return kurt;
  };

  const formatNumber = (num) => {
    if (num === null || num === undefined || isNaN(num)) return 'غير متاح';
    return num.toLocaleString('ar-SA', { maximumFractionDigits: 2 });
  };

  const getSkewnessLabel = (skewness) => {
    if (skewness > 1) return { label: 'منحرف بشدة لليمين', color: 'error' };
    if (skewness > 0.5) return { label: 'منحرف لليمين', color: 'warning' };
    if (skewness < -1) return { label: 'منحرف بشدة لليسار', color: 'error' };
    if (skewness < -0.5) return { label: 'منحرف لليسار', color: 'warning' };
    return { label: 'متماثل تقريباً', color: 'success' };
  };

  const numericStats = Object.values(statistics).filter(stat => stat.isNumeric !== false);
  const categoricalStats = Object.values(statistics).filter(stat => stat.isNumeric === false);

  return (
    <Paper elevation={3} sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
        <Analytics color="primary" />
        التحليلات الإحصائية المتقدمة
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Assessment sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h6">إجمالي الصفوف</Typography>
              <Typography variant="h4">{data?.length || 0}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Functions sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h6">الأعمدة الرقمية</Typography>
              <Typography variant="h4">{numericStats.length}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Calculate sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h6">الأعمدة النصية</Typography>
              <Typography variant="h4">{categoricalStats.length}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', color: 'white' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h6">إجمالي الأعمدة</Typography>
              <Typography variant="h4">{columns?.length || 0}</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Numeric Statistics Table */}
      {numericStats.length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Functions color="primary" />
            الإحصائيات الرقمية
          </Typography>
          <TableContainer component={Paper} elevation={1}>
            <Table size="small">
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                  <TableCell><strong>العمود</strong></TableCell>
                  <TableCell align="center"><strong>العدد</strong></TableCell>
                  <TableCell align="center"><strong>المجموع</strong></TableCell>
                  <TableCell align="center"><strong>المتوسط</strong></TableCell>
                  <TableCell align="center"><strong>الوسيط</strong></TableCell>
                  <TableCell align="center"><strong>الحد الأدنى</strong></TableCell>
                  <TableCell align="center"><strong>الحد الأقصى</strong></TableCell>
                  <TableCell align="center"><strong>الانحراف المعياري</strong></TableCell>
                  <TableCell align="center"><strong>الانحراف</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {numericStats.map((stat, index) => {
                  const skewnessInfo = getSkewnessLabel(stat.skewness);
                  return (
                    <TableRow key={index} hover>
                      <TableCell component="th" scope="row">
                        <strong>{stat.columnName}</strong>
                      </TableCell>
                      <TableCell align="center">{stat.count}</TableCell>
                      <TableCell align="center">{formatNumber(stat.sum)}</TableCell>
                      <TableCell align="center">{formatNumber(stat.mean)}</TableCell>
                      <TableCell align="center">{formatNumber(stat.median)}</TableCell>
                      <TableCell align="center">{formatNumber(stat.min)}</TableCell>
                      <TableCell align="center">{formatNumber(stat.max)}</TableCell>
                      <TableCell align="center">{formatNumber(stat.stdDev)}</TableCell>
                      <TableCell align="center">
                        <Chip 
                          label={skewnessInfo.label} 
                          color={skewnessInfo.color} 
                          size="small" 
                          variant="outlined"
                        />
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      )}

      {/* Categorical Statistics */}
      {categoricalStats.length > 0 && (
        <Box>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Assessment color="primary" />
            الإحصائيات النصية
          </Typography>
          <Grid container spacing={2}>
            {categoricalStats.map((stat, index) => (
              <Grid item xs={12} md={6} lg={4} key={index}>
                <Card elevation={1}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom color="primary">
                      {stat.columnName}
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        إجمالي القيم: {stat.count}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        القيم الفريدة: {stat.uniqueCount}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        الأكثر تكراراً: <strong>{stat.mostFrequent}</strong>
                      </Typography>
                    </Box>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="body2" gutterBottom>
                      توزيع القيم:
                    </Typography>
                    {Object.entries(stat.valueCounts).slice(0, 5).map(([value, count]) => (
                      <Box key={value} sx={{ mb: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                          <Typography variant="body2" noWrap>
                            {value.length > 20 ? `${value.substring(0, 20)}...` : value}
                          </Typography>
                          <Typography variant="body2">
                            {count} ({((count / stat.count) * 100).toFixed(1)}%)
                          </Typography>
                        </Box>
                        <LinearProgress 
                          variant="determinate" 
                          value={(count / stat.count) * 100} 
                          sx={{ height: 4, borderRadius: 2 }}
                        />
                      </Box>
                    ))}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
    </Paper>
  );
};

export default StatisticsPanel;
