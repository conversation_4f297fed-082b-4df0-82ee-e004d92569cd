import React, { useState, useMemo } from 'react';
import {
  Paper,
  Typography,
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Tabs,
  Tab,
  <PERSON>ton,
  Stack,
  Chip
} from '@mui/material';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  ScatterChart,
  Scatter
} from 'recharts';
import Plot from 'react-plotly.js';
import AdvancedCharts from './AdvancedCharts';
import {
  BarChart as BarChartIcon,
  ShowChart as LineChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  ScatterPlot as ScatterIcon,
  Timeline as AreaIcon,
  AutoGraph as AdvancedIcon
} from '@mui/icons-material';
import { Alert } from '@mui/material';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#FF7C7C'];

const ChartDashboard = ({ data, columns, autoAnalysis }) => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [xAxisColumn, setXAxisColumn] = useState('');
  const [yAxisColumn, setYAxisColumn] = useState('');
  const [chartType, setChartType] = useState('bar');

  // تطبيق التحليل التلقائي عند توفر البيانات
  React.useEffect(() => {
    if (autoAnalysis && !xAxisColumn && !yAxisColumn) {
      console.log('تطبيق التحليل التلقائي للرسومات البيانية');
      setXAxisColumn(autoAnalysis.recommendedXColumn);
      setYAxisColumn(autoAnalysis.recommendedYColumn);

      // اختيار أفضل نوع رسم بياني حسب البيانات
      if (autoAnalysis.categoricalColumns.length > 0 && autoAnalysis.numericColumns.length > 0) {
        setChartType('bar'); // أعمدة بيانية للبيانات المختلطة
      } else if (autoAnalysis.numericColumns.length >= 2) {
        setChartType('line'); // خط بياني للبيانات الرقمية
      } else {
        setChartType('pie'); // دائري للبيانات التصنيفية
      }
    }
  }, [autoAnalysis, xAxisColumn, yAxisColumn]);

  // Get numeric columns for Y-axis
  const numericColumns = useMemo(() => {
    if (!data || data.length === 0) return [];
    
    return columns.filter(col => {
      const sampleValues = data.slice(0, 10).map(row => row[col.field]);
      return sampleValues.some(val => !isNaN(val) && !isNaN(parseFloat(val)) && val !== '');
    });
  }, [data, columns]);

  // Get categorical columns for X-axis
  const categoricalColumns = useMemo(() => {
    return columns.filter(col => !numericColumns.find(numCol => numCol.field === col.field));
  }, [columns, numericColumns]);

  // Prepare chart data
  const chartData = useMemo(() => {
    if (!data || !xAxisColumn || !yAxisColumn) return [];
    
    const grouped = data.reduce((acc, row) => {
      const xValue = row[xAxisColumn] || 'غير محدد';
      const yValue = parseFloat(row[yAxisColumn]) || 0;
      
      if (!acc[xValue]) {
        acc[xValue] = { name: xValue, value: 0, count: 0 };
      }
      acc[xValue].value += yValue;
      acc[xValue].count += 1;
      
      return acc;
    }, {});
    
    return Object.values(grouped).map(item => ({
      ...item,
      average: item.value / item.count
    }));
  }, [data, xAxisColumn, yAxisColumn]);

  // Statistics
  const statistics = useMemo(() => {
    if (!data || !yAxisColumn) return {};
    
    const values = data.map(row => parseFloat(row[yAxisColumn]) || 0).filter(val => !isNaN(val));
    
    return {
      total: values.reduce((sum, val) => sum + val, 0),
      average: values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0,
      min: Math.min(...values),
      max: Math.max(...values),
      count: values.length
    };
  }, [data, yAxisColumn]);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const renderRechartsChart = () => {
    if (!chartData.length) return <Typography>اختر الأعمدة لعرض الرسم البياني</Typography>;

    const commonProps = {
      width: '100%',
      height: 400,
      data: chartData,
      margin: { top: 20, right: 30, left: 20, bottom: 5 }
    };

    switch (chartType) {
      case 'bar':
        return (
          <ResponsiveContainer {...commonProps}>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="value" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        );
      
      case 'line':
        return (
          <ResponsiveContainer {...commonProps}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="value" stroke="#8884d8" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        );
      
      case 'area':
        return (
          <ResponsiveContainer {...commonProps}>
            <AreaChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Area type="monotone" dataKey="value" stroke="#8884d8" fill="#8884d8" />
            </AreaChart>
          </ResponsiveContainer>
        );
      
      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={120}
                fill="#8884d8"
                dataKey="value"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        );
      
      default:
        return null;
    }
  };

  const renderPlotlyChart = () => {
    if (!chartData.length) return <Typography>اختر الأعمدة لعرض الرسم البياني</Typography>;

    const plotData = [{
      x: chartData.map(d => d.name),
      y: chartData.map(d => d.value),
      type: 'scatter',
      mode: 'markers+lines',
      marker: { 
        color: chartData.map((_, i) => COLORS[i % COLORS.length]),
        size: 10
      },
      line: { color: '#1976d2', width: 3 }
    }];

    const layout = {
      title: `${columns.find(c => c.field === yAxisColumn)?.headerName} حسب ${columns.find(c => c.field === xAxisColumn)?.headerName}`,
      xaxis: { title: columns.find(c => c.field === xAxisColumn)?.headerName },
      yaxis: { title: columns.find(c => c.field === yAxisColumn)?.headerName },
      font: { family: 'Arial, sans-serif' },
      plot_bgcolor: '#f8f9fa',
      paper_bgcolor: '#ffffff'
    };

    return (
      <Plot
        data={plotData}
        layout={layout}
        style={{ width: '100%', height: '400px' }}
        config={{ responsive: true, displayModeBar: true }}
      />
    );
  };

  return (
    <Paper elevation={3} sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        📊 لوحة الرسومات البيانية المتقدمة
      </Typography>

      {/* إشعار التحليل التلقائي */}
      {autoAnalysis && xAxisColumn && yAxisColumn && (
        <Alert severity="success" sx={{ mb: 3 }}>
          <Typography variant="body2">
            🎯 <strong>تم التحليل التلقائي!</strong> تم اختيار أفضل الأعمدة للرسم البياني تلقائياً.
          </Typography>
          <Typography variant="caption" display="block" sx={{ mt: 1 }}>
            المحور السيني: <strong>{columns.find(c => c.field === xAxisColumn)?.headerName}</strong> |
            المحور الصادي: <strong>{columns.find(c => c.field === yAxisColumn)?.headerName}</strong>
          </Typography>
        </Alert>
      )}

      {/* Controls */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel>المحور السيني (X)</InputLabel>
            <Select
              value={xAxisColumn}
              onChange={(e) => setXAxisColumn(e.target.value)}
              label="المحور السيني (X)"
            >
              {categoricalColumns.map((col) => (
                <MenuItem key={col.field} value={col.field}>
                  {col.headerName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel>المحور الصادي (Y)</InputLabel>
            <Select
              value={yAxisColumn}
              onChange={(e) => setYAxisColumn(e.target.value)}
              label="المحور الصادي (Y)"
            >
              {numericColumns.map((col) => (
                <MenuItem key={col.field} value={col.field}>
                  {col.headerName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel>نوع الرسم البياني</InputLabel>
            <Select
              value={chartType}
              onChange={(e) => setChartType(e.target.value)}
              label="نوع الرسم البياني"
            >
              <MenuItem value="bar">أعمدة بيانية</MenuItem>
              <MenuItem value="line">خط بياني</MenuItem>
              <MenuItem value="area">منطقة</MenuItem>
              <MenuItem value="pie">دائري</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Stack direction="row" spacing={1}>
            {statistics.count > 0 && (
              <Chip label={`العدد: ${statistics.count}`} color="primary" variant="outlined" />
            )}
          </Stack>
        </Grid>
      </Grid>

      {/* Statistics Cards */}
      {yAxisColumn && statistics.count > 0 && (
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography color="textSecondary" gutterBottom>المجموع</Typography>
                <Typography variant="h6">{statistics.total.toLocaleString()}</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography color="textSecondary" gutterBottom>المتوسط</Typography>
                <Typography variant="h6">{statistics.average.toFixed(2)}</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography color="textSecondary" gutterBottom>الحد الأدنى</Typography>
                <Typography variant="h6">{statistics.min.toLocaleString()}</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography color="textSecondary" gutterBottom>الحد الأقصى</Typography>
                <Typography variant="h6">{statistics.max.toLocaleString()}</Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Chart Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={selectedTab} onChange={handleTabChange}>
          <Tab label="Recharts" icon={<BarChartIcon />} />
          <Tab label="Plotly" icon={<ScatterIcon />} />
          <Tab label="Chart.js المتقدم" icon={<AdvancedIcon />} />
        </Tabs>
      </Box>

      {/* Chart Content */}
      <Box sx={{ mt: 2 }}>
        {selectedTab === 0 && renderRechartsChart()}
        {selectedTab === 1 && renderPlotlyChart()}
        {selectedTab === 2 && (
          <AdvancedCharts
            data={filteredData || data}
            columns={columns}
            selectedXColumn={xAxisColumn}
            selectedYColumn={yAxisColumn}
            autoAnalysis={autoAnalysis}
          />
        )}
      </Box>
    </Paper>
  );
};

export default ChartDashboard;
