import type { IFilter } from '../../../interfaces/iFilter';
import { Component } from '../../../widgets/component';
import type { IFloatingFilterComp, IFloatingFilterParams, IFloatingFilterParent } from '../floatingFilter';
export declare class ReadOnlyFloatingFilter extends Component implements IFloatingFilterComp<IFilter & IFloatingFilterParent> {
    private readonly eFloatingFilterText;
    private params;
    constructor();
    init(params: IFloatingFilterParams): void;
    onParentModelChanged(parentModel: any): void;
    refresh(params: IFloatingFilterParams): void;
}
