export declare const columnFiltersCSS = ".ag-set-filter{--ag-indentation-level:0}.ag-set-filter-item{align-items:center;display:flex;height:100%}:where(.ag-ltr) .ag-set-filter-item{padding-left:calc(var(--ag-widget-container-horizontal-padding) + var(--ag-indentation-level)*var(--ag-set-filter-indent-size))}:where(.ag-rtl) .ag-set-filter-item{padding-right:calc(var(--ag-widget-container-horizontal-padding) + var(--ag-indentation-level)*var(--ag-set-filter-indent-size))}.ag-set-filter-item-checkbox{display:flex;height:100%;width:100%}.ag-set-filter-group-icons{display:block;>*{cursor:pointer}}:where(.ag-ltr) .ag-set-filter-group-icons{margin-right:var(--ag-widget-container-horizontal-padding)}:where(.ag-rtl) .ag-set-filter-group-icons{margin-left:var(--ag-widget-container-horizontal-padding)}.ag-filter-body-wrapper{display:flex;flex-direction:column}:where(.ag-menu:not(.ag-tabs) .ag-filter) .ag-filter-body-wrapper,:where(.ag-menu:not(.ag-tabs) .ag-filter)>:not(.ag-filter-wrapper){min-width:180px}.ag-filter-filter{flex:1 1 0px}.ag-filter-condition{display:flex;justify-content:center}.ag-floating-filter-body{display:flex;flex:1 1 auto;height:100%;position:relative}.ag-floating-filter-full-body{align-items:center;display:flex;flex:1 1 auto;height:100%;overflow:hidden;width:100%}:where(.ag-floating-filter-full-body)>div{flex:1 1 auto}.ag-floating-filter-input{align-items:center;display:flex;width:100%;>*{flex:1 1 auto}:where(.ag-input-field-input[type=date]),:where(.ag-input-field-input[type=datetime-local]){width:1px}}.ag-floating-filter-button{display:flex;flex:none}.ag-date-floating-filter-wrapper{display:flex}.ag-set-floating-filter-input :where(input)[disabled]{pointer-events:none}.ag-floating-filter-button-button{-webkit-appearance:none;-moz-appearance:none;appearance:none;border:none;height:var(--ag-icon-size);width:var(--ag-icon-size)}.ag-filter-loading{align-items:unset;background-color:var(--ag-chrome-background-color);height:100%;padding:var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);position:absolute;width:100%;z-index:1;:where(.ag-menu) &{background-color:var(--ag-menu-background-color)}}.ag-filter-separator{border-top:solid var(--ag-border-width) var(--menu-separator-color)}:where(.ag-filter-select) .ag-picker-field-wrapper{width:0}.ag-filter-condition-operator{height:17px}:where(.ag-ltr) .ag-filter-condition-operator-or{margin-left:calc(var(--ag-spacing)*2)}:where(.ag-rtl) .ag-filter-condition-operator-or{margin-right:calc(var(--ag-spacing)*2)}.ag-set-filter-select-all{padding-top:var(--ag-widget-container-vertical-padding)}.ag-filter-no-matches,.ag-set-filter-list{height:calc(var(--ag-list-item-height)*6)}.ag-filter-no-matches{padding:var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding)}.ag-set-filter-tree-list{height:calc(var(--ag-list-item-height)*10)}.ag-set-filter-filter{margin-left:var(--ag-widget-container-horizontal-padding);margin-right:var(--ag-widget-container-horizontal-padding);margin-top:var(--ag-widget-container-vertical-padding)}.ag-filter-to{margin-top:var(--ag-widget-vertical-spacing)}.ag-mini-filter{margin:var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding)}:where(.ag-ltr) .ag-set-filter-add-group-indent{margin-left:calc(var(--ag-icon-size) + var(--ag-widget-container-horizontal-padding))}:where(.ag-rtl) .ag-set-filter-add-group-indent{margin-right:calc(var(--ag-icon-size) + var(--ag-widget-container-horizontal-padding))}:where(.ag-filter-menu) .ag-set-filter-list{min-width:200px}.ag-filter-virtual-list-item:focus-visible{box-shadow:inset var(--ag-focus-shadow)}.ag-filter-apply-panel{display:flex;justify-content:flex-end;overflow:hidden;padding:var(--ag-widget-vertical-spacing) var(--ag-widget-container-horizontal-padding) var(--ag-widget-container-vertical-padding)}.ag-filter-apply-panel-button{line-height:1.5}:where(.ag-ltr) .ag-filter-apply-panel-button{margin-left:calc(var(--ag-spacing)*2)}:where(.ag-rtl) .ag-filter-apply-panel-button{margin-right:calc(var(--ag-spacing)*2)}.ag-simple-filter-body-wrapper{display:flex;flex-direction:column;min-height:calc(var(--ag-list-item-height) + var(--ag-widget-container-vertical-padding) + var(--ag-widget-vertical-spacing));overflow-y:auto;padding:var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);padding-bottom:calc(var(--ag-widget-container-vertical-padding) - var(--ag-widget-vertical-spacing));&>*{margin-bottom:var(--ag-widget-vertical-spacing)}:where(.ag-resizer-wrapper){margin:0}}.ag-multi-filter-menu-item{margin:var(--ag-spacing) 0}.ag-multi-filter-group-title-bar{background-color:transparent;color:var(--ag-header-text-color);font-weight:500;padding:calc(var(--ag-spacing)*1.5) var(--ag-spacing)}.ag-group-filter-field-select-wrapper{display:flex;flex-direction:column;gap:var(--ag-widget-vertical-spacing);padding:var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding)}";
