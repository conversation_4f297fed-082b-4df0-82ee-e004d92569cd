/** Map of public events to their handler names in GridOptions */
export declare const _PUBLIC_EVENT_HANDLERS_MAP: Record<"filterChanged" | "sortChanged" | "columnRowGroupChanged" | "columnPivotChanged" | "columnValueChanged" | "rowDataUpdated" | "columnEverythingChanged" | "newColumnsLoaded" | "columnPivotModeChanged" | "pivotMaxColumnsExceeded" | "expandOrCollapseAll" | "gridColumnsChanged" | "columnMoved" | "columnVisible" | "columnPinned" | "columnGroupOpened" | "columnResized" | "displayedColumnsChanged" | "virtualColumnsChanged" | "columnHeaderMouseOver" | "columnHeaderMouseLeave" | "columnHeaderClicked" | "columnHeaderContextMenu" | "asyncTransactionsFlushed" | "rowGroupOpened" | "pinnedRowDataChanged" | "pinnedRowsChanged" | "rangeSelectionChanged" | "cellSelectionChanged" | "chartCreated" | "chartRangeSelectionChanged" | "chartOptionsChanged" | "chartDestroyed" | "toolPanelVisibleChanged" | "toolPanelSizeChanged" | "modelUpdated" | "cutStart" | "cutEnd" | "pasteStart" | "pasteEnd" | "fillStart" | "fillEnd" | "cellSelectionDeleteStart" | "cellSelectionDeleteEnd" | "rangeDeleteStart" | "rangeDeleteEnd" | "undoStarted" | "undoEnded" | "redoStarted" | "redoEnded" | "cellClicked" | "cellDoubleClicked" | "cellMouseDown" | "cellContextMenu" | "cellValueChanged" | "cellEditRequest" | "rowValueChanged" | "headerFocused" | "cellFocused" | "rowSelected" | "selectionChanged" | "tooltipShow" | "tooltipHide" | "cellKeyDown" | "cellMouseOver" | "cellMouseOut" | "filterModified" | "filterUiChanged" | "filterOpened" | "floatingFilterUiChanged" | "advancedFilterBuilderVisibleChanged" | "virtualRowRemoved" | "rowClicked" | "rowDoubleClicked" | "gridReady" | "gridPreDestroyed" | "gridSizeChanged" | "viewportChanged" | "firstDataRendered" | "dragStarted" | "dragStopped" | "dragCancelled" | "rowEditingStarted" | "rowEditingStopped" | "cellEditingStarted" | "cellEditingStopped" | "bodyScroll" | "bodyScrollEnd" | "paginationChanged" | "componentStateChanged" | "storeRefreshed" | "stateUpdated" | "columnMenuVisibleChanged" | "contextMenuVisibleChanged" | "rowDragEnter" | "rowDragMove" | "rowDragLeave" | "rowDragEnd" | "rowDragCancel" | "findChanged" | "rowResizeStarted" | "rowResizeEnded" | "columnsReset", "onToolPanelVisibleChanged" | "onToolPanelSizeChanged" | "onColumnMenuVisibleChanged" | "onContextMenuVisibleChanged" | "onCutStart" | "onCutEnd" | "onPasteStart" | "onPasteEnd" | "onColumnVisible" | "onColumnPinned" | "onColumnResized" | "onColumnMoved" | "onColumnValueChanged" | "onColumnPivotModeChanged" | "onColumnPivotChanged" | "onColumnGroupOpened" | "onNewColumnsLoaded" | "onGridColumnsChanged" | "onDisplayedColumnsChanged" | "onVirtualColumnsChanged" | "onColumnEverythingChanged" | "onColumnsReset" | "onColumnHeaderMouseOver" | "onColumnHeaderMouseLeave" | "onColumnHeaderClicked" | "onColumnHeaderContextMenu" | "onComponentStateChanged" | "onCellValueChanged" | "onCellEditRequest" | "onRowValueChanged" | "onCellEditingStarted" | "onCellEditingStopped" | "onRowEditingStarted" | "onRowEditingStopped" | "onUndoStarted" | "onUndoEnded" | "onRedoStarted" | "onRedoEnded" | "onCellSelectionDeleteStart" | "onCellSelectionDeleteEnd" | "onRangeDeleteStart" | "onRangeDeleteEnd" | "onFillStart" | "onFillEnd" | "onFilterOpened" | "onFilterChanged" | "onFilterModified" | "onFilterUiChanged" | "onFloatingFilterUiChanged" | "onAdvancedFilterBuilderVisibleChanged" | "onFindChanged" | "onChartCreated" | "onChartRangeSelectionChanged" | "onChartOptionsChanged" | "onChartDestroyed" | "onCellKeyDown" | "onGridReady" | "onGridPreDestroyed" | "onFirstDataRendered" | "onGridSizeChanged" | "onModelUpdated" | "onVirtualRowRemoved" | "onViewportChanged" | "onBodyScroll" | "onBodyScrollEnd" | "onDragStarted" | "onDragStopped" | "onDragCancelled" | "onStateUpdated" | "onPaginationChanged" | "onRowDragEnter" | "onRowDragMove" | "onRowDragLeave" | "onRowDragEnd" | "onRowDragCancel" | "onRowResizeStarted" | "onRowResizeEnded" | "onColumnRowGroupChanged" | "onRowGroupOpened" | "onExpandOrCollapseAll" | "onPivotMaxColumnsExceeded" | "onPinnedRowDataChanged" | "onPinnedRowsChanged" | "onRowDataUpdated" | "onAsyncTransactionsFlushed" | "onStoreRefreshed" | "onHeaderFocused" | "onCellClicked" | "onCellDoubleClicked" | "onCellFocused" | "onCellMouseOver" | "onCellMouseOut" | "onCellMouseDown" | "onRowClicked" | "onRowDoubleClicked" | "onRowSelected" | "onSelectionChanged" | "onCellContextMenu" | "onRangeSelectionChanged" | "onCellSelectionChanged" | "onTooltipShow" | "onTooltipHide" | "onSortChanged">;
