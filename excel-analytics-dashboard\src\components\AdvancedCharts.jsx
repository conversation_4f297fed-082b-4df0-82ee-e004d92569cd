import React, { useMemo } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  Filler
} from 'chart.js';
import {
  Bar,
  Line,
  Pie,
  Doughnut,
  Radar,
  PolarArea,
  Bubble
} from 'react-chartjs-2';
import {
  Paper,
  Typography,
  Grid,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Chip,
  Stack
} from '@mui/material';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  Filler
);

const AdvancedCharts = ({ data, columns, selectedXColumn, selectedYColumn }) => {
  // Chart colors
  const colors = [
    'rgba(255, 99, 132, 0.8)',
    'rgba(54, 162, 235, 0.8)',
    'rgba(255, 205, 86, 0.8)',
    'rgba(75, 192, 192, 0.8)',
    'rgba(153, 102, 255, 0.8)',
    'rgba(255, 159, 64, 0.8)',
    'rgba(199, 199, 199, 0.8)',
    'rgba(83, 102, 255, 0.8)'
  ];

  const borderColors = [
    'rgba(255, 99, 132, 1)',
    'rgba(54, 162, 235, 1)',
    'rgba(255, 205, 86, 1)',
    'rgba(75, 192, 192, 1)',
    'rgba(153, 102, 255, 1)',
    'rgba(255, 159, 64, 1)',
    'rgba(199, 199, 199, 1)',
    'rgba(83, 102, 255, 1)'
  ];

  // Prepare chart data
  const chartData = useMemo(() => {
    if (!data || !selectedXColumn || !selectedYColumn) return null;

    const grouped = data.reduce((acc, row) => {
      const xValue = row[selectedXColumn] || 'غير محدد';
      const yValue = parseFloat(row[selectedYColumn]) || 0;
      
      if (!acc[xValue]) {
        acc[xValue] = { total: 0, count: 0 };
      }
      acc[xValue].total += yValue;
      acc[xValue].count += 1;
      
      return acc;
    }, {});

    const labels = Object.keys(grouped);
    const values = Object.values(grouped).map(item => item.total);
    const averages = Object.values(grouped).map(item => item.total / item.count);

    return {
      labels,
      datasets: [
        {
          label: columns.find(c => c.field === selectedYColumn)?.headerName || 'القيم',
          data: values,
          backgroundColor: colors.slice(0, labels.length),
          borderColor: borderColors.slice(0, labels.length),
          borderWidth: 2,
          tension: 0.4,
          fill: false
        }
      ],
      averages,
      totals: values
    };
  }, [data, selectedXColumn, selectedYColumn, columns]);

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          font: {
            family: 'Arial, sans-serif',
            size: 12
          }
        }
      },
      title: {
        display: true,
        text: `${columns.find(c => c.field === selectedYColumn)?.headerName} حسب ${columns.find(c => c.field === selectedXColumn)?.headerName}`,
        font: {
          family: 'Arial, sans-serif',
          size: 16,
          weight: 'bold'
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true
      }
    },
    scales: {
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          font: {
            family: 'Arial, sans-serif'
          }
        }
      },
      y: {
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          font: {
            family: 'Arial, sans-serif'
          }
        }
      }
    }
  };

  // Radar chart data
  const radarData = useMemo(() => {
    if (!chartData) return null;

    return {
      labels: chartData.labels,
      datasets: [
        {
          label: 'القيم',
          data: chartData.totals,
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 2,
          pointBackgroundColor: 'rgba(54, 162, 235, 1)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(54, 162, 235, 1)'
        }
      ]
    };
  }, [chartData]);

  // Bubble chart data
  const bubbleData = useMemo(() => {
    if (!data || !selectedXColumn || !selectedYColumn) return null;

    const bubblePoints = data.slice(0, 50).map((row, index) => ({
      x: parseFloat(row[selectedXColumn]) || index,
      y: parseFloat(row[selectedYColumn]) || 0,
      r: Math.abs(parseFloat(row[selectedYColumn]) || 0) / 100 + 5
    }));

    return {
      datasets: [
        {
          label: 'نقاط البيانات',
          data: bubblePoints,
          backgroundColor: 'rgba(255, 99, 132, 0.6)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }
      ]
    };
  }, [data, selectedXColumn, selectedYColumn]);

  if (!chartData) {
    return (
      <Paper elevation={3} sx={{ p: 3 }}>
        <Typography variant="h6" color="text.secondary" textAlign="center">
          اختر الأعمدة لعرض الرسومات البيانية المتقدمة
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper elevation={3} sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom sx={{ mb: 3, textAlign: 'center' }}>
        🎨 الرسومات البيانية المتقدمة - Chart.js
      </Typography>

      {/* Statistics Summary */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6">إجمالي النقاط</Typography>
              <Typography variant="h4">{chartData.labels.length}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6">أعلى قيمة</Typography>
              <Typography variant="h4">{Math.max(...chartData.totals).toFixed(0)}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6">أقل قيمة</Typography>
              <Typography variant="h4">{Math.min(...chartData.totals).toFixed(0)}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', color: 'white' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6">المتوسط</Typography>
              <Typography variant="h4">
                {(chartData.totals.reduce((a, b) => a + b, 0) / chartData.totals.length).toFixed(0)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Grid */}
      <Grid container spacing={3}>
        {/* Bar Chart */}
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ textAlign: 'center' }}>
                📊 الرسم البياني العمودي
              </Typography>
              <Box sx={{ height: 300 }}>
                <Bar data={chartData} options={chartOptions} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Line Chart */}
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ textAlign: 'center' }}>
                📈 الرسم البياني الخطي
              </Typography>
              <Box sx={{ height: 300 }}>
                <Line data={chartData} options={chartOptions} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Pie Chart */}
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ textAlign: 'center' }}>
                🥧 الرسم البياني الدائري
              </Typography>
              <Box sx={{ height: 300 }}>
                <Pie data={chartData} options={{ ...chartOptions, scales: undefined }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Doughnut Chart */}
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ textAlign: 'center' }}>
                🍩 الرسم البياني الحلقي
              </Typography>
              <Box sx={{ height: 300 }}>
                <Doughnut data={chartData} options={{ ...chartOptions, scales: undefined }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Radar Chart */}
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ textAlign: 'center' }}>
                🎯 الرسم البياني الرادار
              </Typography>
              <Box sx={{ height: 300 }}>
                <Radar data={radarData} options={{ ...chartOptions, scales: undefined }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Polar Area Chart */}
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ textAlign: 'center' }}>
                🌟 الرسم البياني القطبي
              </Typography>
              <Box sx={{ height: 300 }}>
                <PolarArea data={chartData} options={{ ...chartOptions, scales: undefined }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default AdvancedCharts;
