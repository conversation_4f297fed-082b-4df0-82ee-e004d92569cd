Autoscale                                              // components/modebar/buttons.js:203
Box Select                                             // components/modebar/buttons.js:111
Click to enter Colorscale title                        // plots/plots.js:318
Click to enter Component A title                       // plots/ternary/ternary.js:379
Click to enter Component B title                       // plots/ternary/ternary.js:389
Click to enter Component C title                       // plots/ternary/ternary.js:399
Click to enter Plot subtitle                           // plots/plots.js:315
Click to enter Plot title                              // plots/plots.js:314
Click to enter X axis title                            // plots/plots.js:316
Click to enter Y axis title                            // plots/plots.js:317
Click to enter radial axis title                       // plots/polar/polar.js:593
Compare data on hover                                  // components/modebar/buttons.js:234
Double-click on legend to isolate one trace            // components/legend/handle_click.js:21
Double-click to zoom back out                          // plots/ternary/ternary.js:681
Download plot                                          // components/modebar/buttons.js:45
Download plot as a png                                 // components/modebar/buttons.js:44
Draw circle                                            // components/modebar/buttons.js:166
Draw closed freeform                                   // components/modebar/buttons.js:130
Draw line                                              // components/modebar/buttons.js:148
Draw open freeform                                     // components/modebar/buttons.js:139
Draw rectangle                                         // components/modebar/buttons.js:157
Edit in Chart Studio                                   // components/modebar/buttons.js:72
Erase active shape                                     // components/modebar/buttons.js:175
Lasso Select                                           // components/modebar/buttons.js:121
Orbital rotation                                       // components/modebar/buttons.js:341
Pan                                                    // components/modebar/buttons.js:101
Produced with Plotly.js                                // components/modebar/modebar.js:324
Reset                                                  // components/modebar/buttons.js:514
Reset axes                                             // components/modebar/buttons.js:213
Reset camera to default                                // components/modebar/buttons.js:380
Reset camera to last save                              // components/modebar/buttons.js:389
Reset view                                             // components/modebar/buttons.js:586
Reset views                                            // components/modebar/buttons.js:624
Show closest data on hover                             // components/modebar/buttons.js:223
Snapshot succeeded                                     // components/modebar/buttons.js:62
Sorry, there was a problem downloading your snapshot!  // components/modebar/buttons.js:65
Taking snapshot - this may take a few seconds          // components/modebar/buttons.js:52
Toggle Spike Lines                                     // components/modebar/buttons.js:644
Toggle show closest data on hover                      // components/modebar/buttons.js:439
Turntable rotation                                     // components/modebar/buttons.js:350
Zoom                                                   // components/modebar/buttons.js:91
Zoom in                                                // components/modebar/buttons.js:183
Zoom out                                               // components/modebar/buttons.js:193
close:                                                 // traces/ohlc/calc.js:108
concentration:                                         // traces/sankey/plot.js:176
high:                                                  // traces/ohlc/calc.js:106
incoming flow count:                                   // traces/sankey/plot.js:177
kde:                                                   // traces/violin/calc.js:86
lat:                                                   // traces/scattergeo/calc.js:51
lon:                                                   // traces/scattergeo/calc.js:52
low:                                                   // traces/ohlc/calc.js:107
lower fence:                                           // traces/box/calc.js:293
max:                                                   // traces/box/calc.js:289
mean ± σ:                                              // traces/box/calc.js:291
mean:                                                  // traces/box/calc.js:292
median:                                                // traces/box/calc.js:285
min:                                                   // traces/box/calc.js:286
new text                                               // plots/plots.js:319
open:                                                  // traces/ohlc/calc.js:105
outgoing flow count:                                   // traces/sankey/plot.js:178
q1:                                                    // traces/box/calc.js:287
q3:                                                    // traces/box/calc.js:288
source:                                                // traces/sankey/plot.js:174
target:                                                // traces/sankey/plot.js:175
trace                                                  // plots/plots.js:321
upper fence:                                           // traces/box/calc.js:294