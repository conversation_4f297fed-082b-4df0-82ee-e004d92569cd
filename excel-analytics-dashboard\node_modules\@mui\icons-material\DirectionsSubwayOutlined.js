"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M12 2c-4 0-8 .5-8 4v9.5C4 17.43 5.57 19 7.5 19L6 20.5v.5h12v-.5L16.5 19c1.93 0 3.5-1.57 3.5-3.5V6c0-3.5-3.58-4-8-4m5.66 3H6.43c.61-.52 2.06-1 5.57-1 3.71 0 5.12.46 5.66 1M11 7v3H6V7zm2 0h5v3h-5zm3.5 10h-9c-.83 0-1.5-.67-1.5-1.5V12h12v3.5c0 .83-.67 1.5-1.5 1.5"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "8.5",
  cy: "14.5",
  r: "1.5"
}, "1"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "15.5",
  cy: "14.5",
  r: "1.5"
}, "2")], 'DirectionsSubwayOutlined');