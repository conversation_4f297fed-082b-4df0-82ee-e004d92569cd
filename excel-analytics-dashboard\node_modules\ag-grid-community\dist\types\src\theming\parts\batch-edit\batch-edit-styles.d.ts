import type { Part } from '../../Part';
import type { ColorValue } from '../../theme-types';
export type BatchEditStyleParams = {
    /**
     * Background color of the cell when in batch edit mode.
     */
    cellBatchEditBackgroundColor: ColorValue;
    /**
     * Text color of the cell when in batch edit mode.
     */
    cellBatchEditTextColor: ColorValue;
    /**
     * Background color for rows in batch edit mode.
     */
    rowBatchEditBackgroundColor: ColorValue;
    /**
     * Text color for rows in batch edit mode.
     */
    rowBatchEditTextColor: ColorValue;
};
export declare const baseDarkBatchEditParams: BatchEditStyleParams;
export declare const batchEditStyleBase: Part<BatchEditStyleParams>;
export declare const darkBatchEditStyleBase: Part<BatchEditStyleParams>;
