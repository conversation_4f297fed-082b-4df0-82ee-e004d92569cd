import type { BeanCollection } from '../context/context';
export declare function paginationIsLastPageFound(beans: BeanCollection): boolean;
export declare function paginationGetPageSize(beans: BeanCollection): number;
export declare function paginationGetCurrentPage(beans: BeanCollection): number;
export declare function paginationGetTotalPages(beans: BeanCollection): number;
export declare function paginationGetRowCount(beans: BeanCollection): number;
export declare function paginationGoToNextPage(beans: BeanCollection): void;
export declare function paginationGoToPreviousPage(beans: BeanCollection): void;
export declare function paginationGoToFirstPage(beans: BeanCollection): void;
export declare function paginationGoToLastPage(beans: BeanCollection): void;
export declare function paginationGoToPage(beans: BeanCollection, page: number): void;
