import type { BeanCollection } from '../context/context';
import type { Component } from '../widgets/component';
export declare function _registerKeyboardFocusEvents(beans: BeanCollection): () => void;
export declare function _isKeyboardMode(): boolean;
export declare function _addFocusableContainerListener(beans: BeanCollection, comp: Component, eGui: HTMLElement): void;
export declare function _findFocusableElements(rootNode: HTMLElement, exclude?: string | null, onlyUnmanaged?: boolean): HTMLElement[];
export declare function _focusInto(rootNode: HTMLElement, up?: boolean, onlyUnmanaged?: boolean, excludeTabGuards?: boolean): boolean;
export declare function _findNextFocusableElement(beans: BeanCollection, rootNode: HTMLElement, onlyManaged?: boolean | null, backwards?: boolean): HTMLElement | null;
export declare function _findTabbableParent(node: HTMLElement | null, limit?: number): HTMLElement | null;
export declare function _focusGridInnerElement(beans: BeanCollection, fromBottom?: boolean): boolean;
export declare function _isHeaderFocusSuppressed(beans: BeanCollection): boolean;
export declare function _isCellFocusSuppressed(beans: BeanCollection): boolean;
export declare function _focusNextGridCoreContainer(beans: BeanCollection, backwards: boolean, forceOut?: boolean): boolean;
