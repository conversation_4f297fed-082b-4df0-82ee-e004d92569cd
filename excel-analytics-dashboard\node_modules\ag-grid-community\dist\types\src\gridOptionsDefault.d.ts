import type { GridOptions } from './entities/gridOptions';
export declare const GRID_OPTION_DEFAULTS: {
    readonly suppressContextMenu: false;
    readonly preventDefaultOnContextMenu: false;
    readonly allowContextMenuWithControlKey: false;
    readonly suppressMenuHide: true;
    readonly enableBrowserTooltips: false;
    readonly tooltipTrigger: "hover";
    readonly tooltipShowDelay: 2000;
    readonly tooltipHideDelay: 10000;
    readonly tooltipMouseTrack: false;
    readonly tooltipShowMode: "standard";
    readonly tooltipInteraction: false;
    readonly copyHeadersToClipboard: false;
    readonly copyGroupHeadersToClipboard: false;
    readonly clipboardDelimiter: "\t";
    readonly suppressCopyRowsToClipboard: false;
    readonly suppressCopySingleCellRanges: false;
    readonly suppressLastEmptyLineOnPaste: false;
    readonly suppressClipboardPaste: false;
    readonly suppressClipboardApi: false;
    readonly suppressCutToClipboard: false;
    readonly maintainColumnOrder: false;
    readonly enableStrictPivotColumnOrder: false;
    readonly suppressFieldDotNotation: false;
    readonly allowDragFromColumnsToolPanel: false;
    readonly suppressMovableColumns: false;
    readonly suppressColumnMoveAnimation: false;
    readonly suppressMoveWhenColumnDragging: false;
    readonly suppressDragLeaveHidesColumns: false;
    readonly suppressRowGroupHidesColumns: false;
    readonly suppressAutoSize: false;
    readonly autoSizePadding: 20;
    readonly skipHeaderOnAutoSize: false;
    readonly singleClickEdit: false;
    readonly suppressClickEdit: false;
    readonly readOnlyEdit: false;
    readonly stopEditingWhenCellsLoseFocus: false;
    readonly enterNavigatesVertically: false;
    readonly enterNavigatesVerticallyAfterEdit: false;
    readonly enableCellEditingOnBackspace: false;
    readonly undoRedoCellEditing: false;
    readonly undoRedoCellEditingLimit: 10;
    readonly suppressCsvExport: false;
    readonly suppressExcelExport: false;
    readonly cacheQuickFilter: false;
    readonly includeHiddenColumnsInQuickFilter: false;
    readonly excludeChildrenWhenTreeDataFiltering: false;
    readonly enableAdvancedFilter: false;
    readonly includeHiddenColumnsInAdvancedFilter: false;
    readonly enableCharts: false;
    readonly masterDetail: false;
    readonly keepDetailRows: false;
    readonly keepDetailRowsCount: 10;
    readonly detailRowAutoHeight: false;
    readonly tabIndex: 0;
    readonly rowBuffer: 10;
    readonly valueCache: false;
    readonly valueCacheNeverExpires: false;
    readonly enableCellExpressions: false;
    readonly suppressTouch: false;
    readonly suppressFocusAfterRefresh: false;
    readonly suppressBrowserResizeObserver: false;
    readonly suppressPropertyNamesCheck: false;
    readonly suppressChangeDetection: false;
    readonly debug: false;
    readonly suppressLoadingOverlay: false;
    readonly suppressNoRowsOverlay: false;
    readonly pagination: false;
    readonly paginationPageSize: 100;
    readonly paginationPageSizeSelector: true;
    readonly paginationAutoPageSize: false;
    readonly paginateChildRows: false;
    readonly suppressPaginationPanel: false;
    readonly pivotMode: false;
    readonly pivotPanelShow: "never";
    readonly pivotDefaultExpanded: 0;
    readonly pivotSuppressAutoColumn: false;
    readonly suppressExpandablePivotGroups: false;
    readonly functionsReadOnly: false;
    readonly suppressAggFuncInHeader: false;
    readonly alwaysAggregateAtRootLevel: false;
    readonly aggregateOnlyChangedColumns: false;
    readonly suppressAggFilteredOnly: false;
    readonly removePivotHeaderRowWhenSingleValueColumn: false;
    readonly animateRows: true;
    readonly cellFlashDuration: 500;
    readonly cellFadeDuration: 1000;
    readonly allowShowChangeAfterFilter: false;
    readonly domLayout: "normal";
    readonly ensureDomOrder: false;
    readonly enableRtl: false;
    readonly suppressColumnVirtualisation: false;
    readonly suppressMaxRenderedRowRestriction: false;
    readonly suppressRowVirtualisation: false;
    readonly rowDragManaged: false;
    readonly rowDragInsertDelay: 500;
    readonly suppressRowDrag: false;
    readonly suppressMoveWhenRowDragging: false;
    readonly rowDragEntireRow: false;
    readonly rowDragMultiRow: false;
    readonly embedFullWidthRows: false;
    readonly groupDisplayType: "singleColumn";
    readonly groupDefaultExpanded: 0;
    readonly groupMaintainOrder: false;
    readonly groupSelectsChildren: false;
    readonly groupSuppressBlankHeader: false;
    readonly groupSelectsFiltered: false;
    readonly showOpenedGroup: false;
    readonly groupRemoveSingleChildren: false;
    readonly groupRemoveLowestSingleChildren: false;
    readonly groupHideOpenParents: false;
    readonly groupAllowUnbalanced: false;
    readonly rowGroupPanelShow: "never";
    readonly suppressMakeColumnVisibleAfterUnGroup: false;
    readonly treeData: false;
    readonly rowGroupPanelSuppressSort: false;
    readonly suppressGroupRowsSticky: false;
    readonly rowModelType: "clientSide";
    readonly asyncTransactionWaitMillis: 50;
    readonly suppressModelUpdateAfterUpdateTransaction: false;
    readonly cacheOverflowSize: 1;
    readonly infiniteInitialRowCount: 1;
    readonly serverSideInitialRowCount: 1;
    readonly cacheBlockSize: 100;
    readonly maxBlocksInCache: -1;
    readonly maxConcurrentDatasourceRequests: 2;
    readonly blockLoadDebounceMillis: 0;
    readonly purgeClosedRowNodes: false;
    readonly serverSideSortAllLevels: false;
    readonly serverSideOnlyRefreshFilteredGroups: false;
    readonly serverSidePivotResultFieldSeparator: "_";
    readonly viewportRowModelPageSize: 5;
    readonly viewportRowModelBufferSize: 5;
    readonly alwaysShowHorizontalScroll: false;
    readonly alwaysShowVerticalScroll: false;
    readonly debounceVerticalScrollbar: false;
    readonly suppressHorizontalScroll: false;
    readonly suppressScrollOnNewData: false;
    readonly suppressScrollWhenPopupsAreOpen: false;
    readonly suppressAnimationFrame: false;
    readonly suppressMiddleClickScrolls: false;
    readonly suppressPreventDefaultOnMouseWheel: false;
    readonly rowMultiSelectWithClick: false;
    readonly suppressRowDeselection: false;
    readonly suppressRowClickSelection: false;
    readonly suppressCellFocus: false;
    readonly suppressHeaderFocus: false;
    readonly suppressMultiRangeSelection: false;
    readonly enableCellTextSelection: false;
    readonly enableRangeSelection: false;
    readonly enableRangeHandle: false;
    readonly enableFillHandle: false;
    readonly fillHandleDirection: "xy";
    readonly suppressClearOnFillReduction: false;
    readonly accentedSort: false;
    readonly unSortIcon: false;
    readonly suppressMultiSort: false;
    readonly alwaysMultiSort: false;
    readonly suppressMaintainUnsortedOrder: false;
    readonly suppressRowHoverHighlight: false;
    readonly suppressRowTransform: false;
    readonly columnHoverHighlight: false;
    readonly deltaSort: false;
    readonly enableGroupEdit: false;
    readonly groupLockGroupColumns: 0;
    readonly serverSideEnableClientSideSort: false;
    readonly suppressServerSideFullWidthLoadingRow: false;
    readonly pivotMaxGeneratedColumns: -1;
    readonly columnMenu: "new";
    readonly reactiveCustomComponents: true;
    readonly suppressSetFilterByDefault: false;
    readonly rowNumbers: false;
    readonly enableFilterHandlers: false;
};
export type GridOptionOrDefault<K extends keyof GridOptions> = K extends keyof typeof GRID_OPTION_DEFAULTS ? NonNullable<GridOptions[K]> : GridOptions[K];
