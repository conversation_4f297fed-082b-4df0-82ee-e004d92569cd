import type { ComponentSelector } from '../widgets/component';
import { Component } from '../widgets/component';
export declare class GridBodyComp extends Component {
    private readonly eGridRoot;
    private readonly eBodyViewport;
    private readonly eStickyTop;
    private readonly eStickyBottom;
    private readonly eTop;
    private readonly eBottom;
    private readonly eBody;
    private ctrl;
    postConstruct(): void;
    private setRowAnimationCssOnBodyViewport;
}
export declare const GridBodySelector: ComponentSelector;
