import type { LocaleTextFunc } from '../misc/locale/localeUtils';
declare const FILTER_LOCALE_TEXT: {
    applyFilter: string;
    clearFilter: string;
    resetFilter: string;
    cancelFilter: string;
    textFilter: string;
    numberFilter: string;
    dateFilter: string;
    setFilter: string;
    filterOoo: string;
    empty: string;
    equals: string;
    notEqual: string;
    lessThan: string;
    greaterThan: string;
    inRange: string;
    inRangeStart: string;
    inRangeEnd: string;
    lessThanOrEqual: string;
    greaterThanOrEqual: string;
    contains: string;
    notContains: string;
    startsWith: string;
    endsWith: string;
    blank: string;
    notBlank: string;
    before: string;
    after: string;
    andCondition: string;
    orCondition: string;
    dateFormatOoo: string;
    filterSummaryInactive: string;
    filterSummaryContains: string;
    filterSummaryNotContains: string;
    filterSummaryTextEquals: string;
    filterSummaryTextNotEqual: string;
    filterSummaryStartsWith: string;
    filterSummaryEndsWith: string;
    filterSummaryBlank: string;
    filterSummaryNotBlank: string;
    filterSummaryEquals: string;
    filterSummaryNotEqual: string;
    filterSummaryGreaterThan: string;
    filterSummaryGreaterThanOrEqual: string;
    filterSummaryLessThan: string;
    filterSummaryLessThanOrEqual: string;
    filterSummaryInRange: string;
    filterSummaryInRangeValues: (variableValues: string[]) => string;
    filterSummaryTextQuote: (variableValues: string[]) => string;
};
export type FilterLocaleTextKey = keyof typeof FILTER_LOCALE_TEXT;
export declare function translateForFilter(bean: {
    getLocaleTextFunc(): LocaleTextFunc;
}, key: keyof typeof FILTER_LOCALE_TEXT, variableValues?: string[]): string;
export {};
