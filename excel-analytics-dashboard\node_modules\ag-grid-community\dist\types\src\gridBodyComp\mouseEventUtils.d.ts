import type { BeanCollection } from '../context/context';
import type { DraggingEvent } from '../dragAndDrop/dragAndDropService';
import type { GridOptionsService } from '../gridOptionsService';
import type { CellPosition } from '../interfaces/iCellPosition';
export declare function _stampTopLevelGridCompWithGridInstance(gos: GridOptionsService, eGridDiv: HTMLElement): void;
export declare function _isEventFromThisGrid(gos: GridOptionsService, event: UIEvent): boolean;
export declare function _isElementInThisGrid(gos: GridOptionsService, element: HTMLElement): boolean;
export declare function _getCellPositionForEvent(gos: GridOptionsService, event: MouseEvent | KeyboardEvent): CellPosition | null;
export declare function _getNormalisedMousePosition(beans: BeanCollection, event: MouseEvent | DraggingEvent): {
    x: number;
    y: number;
};
