import type { LocaleTextFunc } from '../misc/locale/localeUtils';
/**
 * the native method number.toLocaleString(undefined, {minimumFractionDigits: 0})
 * puts in decimal places in IE, so we use this method instead
 * from: http://blog.tompawlak.org/number-currency-formatting-javascript
 * @param {number} value
 * @returns {string}
 */
export declare function _formatNumberCommas(value: number | null, getLocaleTextFunc: () => LocaleTextFunc): string;
