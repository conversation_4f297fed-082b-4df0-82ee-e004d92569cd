export declare const materialAdjustmentsCSS = ".ag-dnd-ghost,.ag-filter-toolpanel-header,.ag-filter-toolpanel-search,.ag-multi-filter-group-title-bar,.ag-panel-title-bar-title,.ag-status-bar{color:var(--ag-header-text-color);font-size:calc(var(--ag-font-size) - 1px);font-weight:600}.ag-column-drop-horizontal{background-color:color-mix(in srgb,var(--ag-background-color),var(--ag-foreground-color) 8%)}.ag-cell.ag-cell-inline-editing{background-color:var(--ag-background-color);background-image:linear-gradient(0deg,var(--ag-input-background-color),var(--ag-input-background-color));border:var(--ag-input-border)!important;border-width:1px!important;height:calc(var(--ag-row-height) + var(--ag-spacing)*3);padding:var(--ag-spacing);:where(.ag-row-last:not(.ag-row-first)) &{bottom:0}:where(.ag-has-focus) &{border:var(--ag-input-focus-border)!important;border-width:1px!important}}.ag-advanced-filter-builder-button,.ag-standard-button{text-transform:uppercase}.ag-status-bar{border:solid var(--ag-border-width) var(--ag-border-color)}.ag-list-item-hovered:after{background-color:var(--ag-primary-color)}.ag-pill-button:hover{color:var(--ag-primary-color)}.ag-header-highlight-after:after,.ag-header-highlight-before:after{background-color:var(--ag-primary-color)}.ag-filter-add-button,.ag-filter-add-button:hover{border-bottom:2px solid var(--ag-primary-color)}";
