export declare const agSelectCSS = ".ag-select{align-items:center;display:flex;&.ag-disabled{opacity:.5}}:where(.ag-select){.ag-picker-field-wrapper{cursor:default}&.ag-disabled .ag-picker-field-wrapper:focus{box-shadow:none}&:not(.ag-cell-editor,.ag-label-align-top){min-height:var(--ag-list-item-height)}.ag-picker-field-display{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.ag-picker-field-icon{align-items:center;display:flex}}:where(.ag-ltr) :where(.ag-select){.ag-picker-field-wrapper{padding-left:calc(var(--ag-cell-horizontal-padding)/2);padding-right:var(--ag-spacing)}}:where(.ag-rtl) :where(.ag-select){.ag-picker-field-wrapper{padding-left:var(--ag-spacing);padding-right:calc(var(--ag-cell-horizontal-padding)/2)}}";
