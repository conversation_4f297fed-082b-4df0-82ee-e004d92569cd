import type { BeanCollection } from '../context/context';
import type { ValueGetterFunc } from '../entities/colDef';
import type { BaseCellDataType, CoreDataTypeDefinition, DataTypeFormatValueFunc } from '../entities/dataType';
import type { LocaleTextFunc } from '../misc/locale/localeUtils';
export declare function _getFilterParamsForDataType(filter: string, existingFilterParams: any, existingFilterValueGetter: string | ValueGetterFunc | undefined, dataTypeDefinition: CoreDataTypeDefinition, formatValue: DataTypeFormatValueFunc, beans: BeanCollection, translate: LocaleTextFunc): {
    filterParams?: any;
    filterValueGetter?: string | ValueGetterFunc;
};
export declare function _getDefaultSimpleFilter(cellDataType?: BaseCellDataType, isFloating?: boolean): string;
