import type { ColumnModel } from '../columns/columnModel';
import type { BeanCollection } from '../context/context';
export declare function getHeaderRowCount(colModel: ColumnModel): number;
export declare function getFocusHeaderRowCount(beans: BeanCollection): number;
export declare function getGroupRowsHeight(beans: BeanCollection): number[];
export declare function getColumnHeaderRowHeight(beans: BeanCollection): number;
export declare function getHeaderHeight(beans: BeanCollection): number;
export declare function getFloatingFiltersHeight(beans: BeanCollection): number;
