export declare const cellEditingCSS = ".ag-cell-inline-editing{border:var(--ag-cell-editing-border)!important;border-radius:var(--ag-border-radius);box-shadow:var(--ag-cell-editing-shadow);padding:0;z-index:1;.ag-cell-edit-wrapper,.ag-cell-editor,.ag-cell-wrapper,:where(.ag-cell-editor) .ag-input-field-input,:where(.ag-cell-editor) .ag-wrapper{height:100%;line-height:normal;min-height:100%;width:100%}&.ag-cell-editing-error{border-color:var(--ag-invalid-color)!important}}:where(.ag-popup-editor) .ag-large-text{background-color:var(--ag-background-color);border-radius:var(--ag-border-radius);box-shadow:var(--ag-dropdown-shadow);padding:0}.ag-large-text-input{height:auto;padding:var(--ag-cell-horizontal-padding)}:where(.ag-rtl .ag-large-text-input) textarea{resize:none}:where(.ag-ltr) .ag-checkbox-edit{padding-left:var(--ag-cell-horizontal-padding)}:where(.ag-rtl) .ag-checkbox-edit{padding-right:var(--ag-cell-horizontal-padding)}:where(.ag-row.ag-row-editing-invalid .ag-cell-inline-editing){opacity:.8}";
