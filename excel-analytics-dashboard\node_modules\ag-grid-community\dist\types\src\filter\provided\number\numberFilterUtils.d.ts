import type { <PERSON><PERSON> } from '../iSimpleFilter';
import type { OptionsFactory } from '../optionsFactory';
import type { INumberFilterParams, NumberFilterModel } from './iNumberFilter';
export declare function getAllowedCharPattern(filterParams?: INumberFilterParams): string | null;
export declare function processNumberFilterValue(value?: number | null): number | null;
export declare function mapValuesFromNumberFilterModel(filterModel: NumberFilterModel | null, optionsFactory: OptionsFactory): Tuple<number>;
