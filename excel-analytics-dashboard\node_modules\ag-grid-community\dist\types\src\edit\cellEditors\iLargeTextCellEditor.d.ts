import type { ICellEditorParams } from '../../interfaces/iCellEditor';
export interface ILargeTextEditorParams extends ICellEditorParams {
    /**
     * Max number of characters to allow.
     * @default 200
     */
    maxLength: number;
    /**
     * Number of character rows to display.
     * @default 10
     */
    rows: number;
    /**
     * Number of character columns to display.
     * @default 60
     */
    cols: number;
}
