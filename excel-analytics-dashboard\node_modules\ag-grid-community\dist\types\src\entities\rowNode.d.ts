import type { DetailGridInfo } from '../api/gridApi';
import type { BeanCollection } from '../context/context';
import type { SelectionEventSourceType } from '../events';
import type { IServerSideStore } from '../interfaces/IServerSideStore';
import type { IAgEventEmitter, IEventEmitter } from '../interfaces/iEventEmitter';
import type { AgRowNodeEventListener, IRowNode, RowNodeEventType, RowPinnedType } from '../interfaces/iRowNode';
import { LocalEventService } from '../localEventService';
import type { AgColumn } from './agColumn';
export declare const ROW_ID_PREFIX_ROW_GROUP = "row-group-";
export declare const ROW_ID_PREFIX_TOP_PINNED = "t-";
export declare const ROW_ID_PREFIX_BOTTOM_PINNED = "b-";
export declare class RowNode<TData = any> implements IEventEmitter<RowNodeEventType>, IAgEventEmitter<RowNodeEventType>, IRowNode<TData> {
    /** Unique ID for the node. Either provided by the application, or generated by the grid if not. */
    id: string | undefined;
    /** If using row grouping, contains the group values for this group. */
    groupData: {
        [key: string]: any | null;
    } | null;
    /** When using group rows, contains the value without casting to string */
    groupValue: any;
    /** If using row grouping and aggregation, contains the aggregation data. */
    aggData: any;
    /**
     * The data as provided by the application.
     * Can be `undefined` when using row grouping or during grid initialisation.
     */
    data: TData | undefined;
    /** The parent node to this node, or empty if top level */
    parent: RowNode<TData> | null;
    /** How many levels this node is from the top when grouping. */
    level: number;
    /** How many levels this node is from the top when grouping in the UI (only different to `parent` when `groupHideParentOfSingleChild=true`)*/
    uiLevel: number;
    /**
     * If doing in-memory (client-side) grouping, this is the index of the group column this cell is for.
     * This is always the same as the level, unless we are collapsing groups, i.e. `groupHideParentOfSingleChild=true`.
     */
    rowGroupIndex: number | null;
    /** `true` if this node is a group node (i.e. it has children) */
    group: boolean | undefined;
    /** `true` if this row is getting dragged */
    dragging: boolean;
    /** `true` if this row is a master row, part of master / detail (ie row can be expanded to show detail) */
    master: boolean;
    /** `true` if this row is a detail row, part of master / detail (ie child row of an expanded master row)*/
    detail: boolean | undefined;
    /** If this row is a master row that was expanded, this points to the associated detail row. */
    detailNode: RowNode | undefined;
    /** If master detail, this contains details about the detail grid */
    detailGridInfo: DetailGridInfo | null;
    /** `true` if this node is a group and the group is the bottom level in the tree. */
    leafGroup: boolean | undefined;
    firstChild: boolean;
    lastChild: boolean;
    childIndex: number;
    /** The current row index. If the row is filtered out or in a collapsed group, this value is set to `null`. */
    rowIndex: number | null;
    /**
     * Either 'top' or 'bottom' if row pinned, otherwise `undefined` or `null`.
     * If re-naming this property, you must also update `IGNORED_SIBLING_PROPERTIES`
     */
    rowPinned: RowPinnedType;
    /**
     * If using manual row pinning, a reference to the sibling node.
     * If this node is in the pinned section, `pinnedSibling` is the source row.
     * If this node is in the main viewport, `pinnedSibling` is the pinned row.
     */
    pinnedSibling?: RowNode<TData>;
    /** When true, this row sticks to the top */
    sticky: boolean;
    /** If row is pinned, then pinnedRowTop is used rather than rowTop */
    stickyRowTop: number;
    /** If using quick filter, stores a string representation of the row for searching against. */
    quickFilterAggregateText: string | null;
    /** `true` if row is a footer. Footers have `group = true` and `footer = true`. */
    footer: boolean | undefined;
    /** The field we are grouping on eg 'country'. */
    field: string | null;
    /** The row group column used for this group, e.g. the Country column instance. */
    rowGroupColumn: AgColumn | null;
    /** The key for the group eg Ireland, UK, USA */
    key: string | null;
    /** Used by server-side row model. `true` if this row node is a stub. A stub is a placeholder row with loading icon while waiting from row to be loaded. */
    stub: boolean | undefined;
    /** Used by server side row model, true if this row node failed a load */
    failedLoad: boolean | undefined;
    /** Used by server side row model, true if this node needs refreshed by the server when in viewport */
    __needsRefreshWhenVisible: boolean;
    /**
     * The index of the row in the source rowData array including any updates via transactions.
     * It does not change when sorting, filtering, grouping, pivoting or any other UI related operations.
     * If this is a filler node (a visual row created by AG Grid in tree data or grouping) the value is set to `-1`.
     *
     * Generally readonly. It is modified only by:
     * - ClientSideNodeManager, cast to ClientSideNodeManagerRowNode
     * - ClientSideRowModel, cast to ClientSideRowModelRowNode
     */
    readonly sourceRowIndex: number;
    /**
     * All lowest level nodes beneath this node, no groups.
     * In the root node, this array contains all rows, and is computed by the ClientSideRowModel.
     * Do not modify this array directly. The grouping module relies on mutable references to the array.
     * The array might also br frozen (immutable).
     *
     * Generally readonly. It is modified only by:
     * - ClientSideNodeManager, cast to ClientSideNodeManagerRootNode
     * - GroupStrategy, cast to GroupRow
     * - TreeStrategy, cast to TreeRow
     */
    readonly allLeafChildren: RowNode<TData>[] | null;
    /**
     * Children of this group. If multi levels of grouping, shows only immediate children.
     * Do not modify this array directly. The grouping module relies on mutable references to the array.
     *
     * Generally readonly. It is modified only by:
     * - ClientSideNodeManager, cast to ClientSideNodeManagerRootNode
     * - GroupStrategy, cast to GroupRow
     * - TreeStrategy, cast to TreeRow
     */
    readonly childrenAfterGroup: RowNode<TData>[] | null;
    /** Filtered children of this group. */
    childrenAfterFilter: RowNode<TData>[] | null;
    /** Aggregated and re-filtered children of this group. */
    childrenAfterAggFilter: RowNode<TData>[] | null;
    /** Sorted children of this group. */
    childrenAfterSort: RowNode<TData>[] | null;
    /** Number of children and grand children. */
    allChildrenCount: number | null;
    /** Children mapped by the pivot columns or group key */
    childrenMapped: {
        [key: string]: any;
    } | null;
    /**
     * Parent RowNode for tree data.
     * When set, the parent node in the hierarchy is updated during Client-Side Row Model (CSRM) grouping.
     * Used by the ClientSideChildrenTreeNodeManager, TreeGroupStrategy, RowDragFeature
     */
    readonly treeParent: RowNode<TData> | null;
    /** The flags associated to this node. Used only internally within TreeGroupStrategy. */
    readonly treeNodeFlags: number;
    /** Server Side Row Model Only - the children are in an infinite cache. */
    childStore: IServerSideStore | null;
    /** `true` if group is expanded, otherwise `false`. */
    expanded: boolean;
    /** If using footers, reference to the footer node for this group. */
    sibling: RowNode;
    /** The height, in pixels, of this row */
    rowHeight: number | null | undefined;
    /** Dynamic row heights are done on demand, only when row is visible. However for row virtualisation
     * we need a row height to do the 'what rows are in viewport' maths. So we assign a row height to each
     * row based on defaults and rowHeightEstimated=true, then when the row is needed for drawing we do
     * the row height calculation and set rowHeightEstimated=false.*/
    rowHeightEstimated: boolean;
    /**
     * This is `true` if the row has a rowIndex assigned, otherwise `false`.
     */
    displayed: boolean;
    /** The row top position in pixels. */
    rowTop: number | null;
    /** The top pixel for this row last time, makes sense if data set was ordered or filtered,
     * it is used so new rows can animate in from their old position. */
    oldRowTop: number | null;
    /** `true` by default - can be overridden via gridOptions.isRowSelectable(rowNode) */
    selectable: boolean;
    /** `true` if this node is a daemon. This means row is not part of the model. Can happen when then
     * the row is selected and then the user sets a different ID onto the node. The nodes is then
     * representing a different entity, so the selection controller, if the node is selected, takes
     * a copy where daemon=true. */
    __daemon: boolean;
    /** Used by the value service, stores values for a particular change detection turn. */
    __cacheData: {
        [colId: string]: any;
    };
    __cacheVersion: number;
    /**
     * Used by sorting service - to give deterministic sort to groups. Previously we
     * just id for this, however id is a string and had slower sorting compared to numbers.
     * If re-naming this property, you must also update `IGNORED_SIBLING_PROPERTIES`
     */
    __objectId: number;
    /** We cache the result of hasChildren() so that we can be aware of when it has changed, and hence
     * fire the event. Really we should just have hasChildren as an attribute and do away with hasChildren()
     * method, however that would be a breaking change. */
    private __hasChildren;
    /**
     * When one or more Columns are using autoHeight, this keeps track of height of each autoHeight Cell,
     * indexed by the Column ID.
     * If re-naming this property, you must also update `IGNORED_SIBLING_PROPERTIES`
     */
    __autoHeights?: {
        [id: string]: number | undefined;
    };
    /** `true` when nodes with the same id are being removed and added as part of the same batch transaction */
    alreadyRendered: boolean;
    private hovered;
    __selected: boolean | undefined;
    /** If re-naming this property, you must also update `IGNORED_SIBLING_PROPERTIES` */
    __localEventService: LocalEventService<RowNodeEventType> | null;
    private frameworkEventListenerService?;
    private beans;
    /** If re-naming this property, you must also update `IGNORED_SIBLING_PROPERTIES` */
    __checkAutoHeightsDebounced: () => void;
    constructor(beans: BeanCollection);
    /**
     * Replaces the data on the `rowNode`. When this method is called, the grid refreshes the entire rendered row if it is displayed.
     */
    setData(data: TData): void;
    /**
     * Updates the data on the `rowNode`. When this method is called, the grid refreshes the entire rendered row if it is displayed.
     */
    updateData(data: TData): void;
    private setDataCommon;
    private updateDataOnDetailNode;
    private createDataChangedEvent;
    getRowIndexString(): string | null;
    setDataAndId(data: TData, id: string | undefined): void;
    private setId;
    setRowTop(rowTop: number | null): void;
    clearRowTopAndRowIndex(): void;
    setHovered(hovered: boolean): void;
    isHovered(): boolean;
    /**
     * Sets the row height.
     * Call if you want to change the height initially assigned to the row.
     * After calling, you must call `api.onRowHeightChanged()` so the grid knows it needs to work out the placement of the rows. */
    setRowHeight(rowHeight: number | undefined | null, estimated?: boolean): void;
    setExpanded(expanded: boolean, e?: MouseEvent | KeyboardEvent, forceSync?: boolean): void;
    /**
     * Replaces the value on the `rowNode` for the specified column. When complete,
     * the grid refreshes the rendered cell on the required row only.
     * **Note**: This method only fires `onCellEditRequest` when the Grid is in **Read Only** mode.
     * **Note**: This method defers to EditModule if available and batches the edit when `fullRow` or `batchEdit` is enabled.
     *
     * @param colKey The column where the value should be updated
     * @param newValue The new value
     * @param eventSource The source of the event
     * @returns `true` if the value was changed, otherwise `false`.
     */
    setDataValue(colKey: string | AgColumn, newValue: any, eventSource?: string): boolean;
    updateHasChildren(): void;
    hasChildren(): boolean;
    dispatchCellChangedEvent(column: AgColumn, newValue: TData, oldValue: TData): void;
    /**
     * The first time `quickFilter` runs, the grid creates a one-off string representation of the row.
     * This string is then used for the quick filter instead of hitting each column separately.
     * When you edit, using grid editing, this string gets cleared down.
     * However, if you edit without using grid editing, you need to clear this string down for the row to be updated with the new values.
     * Otherwise, new values would not work with the `quickFilter`. */
    resetQuickFilterAggregateText(): void;
    /** Returns:
     * - `true` if the node can be expanded, i.e it is a group or master row.
     * - `false` if the node cannot be expanded
     */
    isExpandable(): boolean;
    /** Returns:
     * - `true` if node is selected,
     * - `false` if the node isn't selected
     * - `undefined` if it's partially selected (group where not all children are selected). */
    isSelected(): boolean | undefined;
    /** Perform a depth-first search of this node and its children. */
    depthFirstSearch(callback: (rowNode: RowNode<TData>) => void): void;
    dispatchRowEvent<T extends RowNodeEventType>(type: T): void;
    /**
     * Select (or deselect) the node.
     * @param newValue -`true` for selection, `false` for deselection.
     * @param clearSelection - If selecting, then passing `true` selects the node exclusively (i.e. NOT do multi select). If doing deselection, `clearSelection` has no impact.
     * @param source - Source property that appears in the `selectionChanged` event.
     */
    setSelected(newValue: boolean, clearSelection?: boolean, source?: SelectionEventSourceType): void;
    /**
     * Returns:
     * - `true` if node is either pinned to the `top` or `bottom`
     * - `false` if the node isn't pinned
     */
    isRowPinned(): boolean;
    __addEventListener<T extends RowNodeEventType>(eventType: T, listener: AgRowNodeEventListener<T>): void;
    __removeEventListener<T extends RowNodeEventType>(eventType: T, listener: AgRowNodeEventListener<T>): void;
    /**
     * PUBLIC USE ONLY: for internal use within AG Grid use the `__addEventListener` and `__removeEventListener` methods.
     */
    addEventListener<T extends RowNodeEventType>(eventType: T, userListener: AgRowNodeEventListener<T>): void;
    /**
     * PUBLIC USE ONLY: for internal use within AG Grid use the `__addEventListener` and `__removeEventListener` methods.
     */
    removeEventListener<T extends RowNodeEventType>(eventType: T, userListener: AgRowNodeEventListener<T>): void;
    private removeLocalListener;
    /**
     * @deprecated v32.2.0 Check `node.detail` then user provided callback `isFullWidthRow` instead.
     *
     * Returns:
     * - `true` if the node is a full width cell
     * - `false` if the node is not a full width cell
     */
    isFullWidthCell(): boolean;
    /**
     * Returns the route of keys to the row node. Returns undefined if the node has no key.
     */
    getRoute(): string[] | undefined;
    setFirstChild(firstChild: boolean): void;
    private setDisplayed;
    setRowIndex(rowIndex: number | null): void;
    setAllChildrenCount(allChildrenCount: number | null): void;
    setUiLevel(uiLevel: number): void;
    getFirstChild(): RowNode<TData> | null;
}
