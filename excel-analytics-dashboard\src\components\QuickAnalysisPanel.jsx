import React from 'react';
import {
  Paper,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Chip,
  LinearProgress,
  Al<PERSON>,
  Divider
} from '@mui/material';
import {
  Analytics,
  TrendingUp,
  Assessment,
  Speed,
  CheckCircle,
  AutoGraph
} from '@mui/icons-material';

const QuickAnalysisPanel = ({ data, columns, autoAnalysis }) => {
  if (!data || !autoAnalysis) {
    return null;
  }

  const { numericColumns, categoricalColumns, totalRows, totalColumns } = autoAnalysis;

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
      <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Speed color="primary" />
        التحليل السريع للبيانات
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          🚀 <strong>تم تحليل البيانات تلقائياً!</strong> إليك ملخص سريع عن ملفك:
        </Typography>
      </Alert>

      {/* إحصائيات سريعة */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Assessment sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h6">{totalRows}</Typography>
              <Typography variant="caption">إجمالي الصفوف</Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Analytics sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h6">{totalColumns}</Typography>
              <Typography variant="caption">إجمالي الأعمدة</Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <TrendingUp sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h6">{numericColumns.length}</Typography>
              <Typography variant="caption">أعمدة رقمية</Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={6} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', color: 'white' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <AutoGraph sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h6">{categoricalColumns.length}</Typography>
              <Typography variant="caption">أعمدة نصية</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* تفاصيل الأعمدة */}
      <Grid container spacing={3}>
        {/* الأعمدة الرقمية */}
        <Grid item xs={12} md={6}>
          <Card elevation={1}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TrendingUp color="primary" />
                الأعمدة الرقمية
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                مناسبة للحسابات والرسومات البيانية
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {numericColumns.map((col, index) => (
                  <Chip
                    key={index}
                    label={col.headerName}
                    color="primary"
                    variant="outlined"
                    size="small"
                    icon={<TrendingUp />}
                  />
                ))}
                {numericColumns.length === 0 && (
                  <Typography variant="body2" color="text.secondary">
                    لا توجد أعمدة رقمية
                  </Typography>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* الأعمدة النصية */}
        <Grid item xs={12} md={6}>
          <Card elevation={1}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Assessment color="secondary" />
                الأعمدة النصية
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                مناسبة للتصنيف والفلترة
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {categoricalColumns.map((col, index) => (
                  <Chip
                    key={index}
                    label={col.headerName}
                    color="secondary"
                    variant="outlined"
                    size="small"
                    icon={<Assessment />}
                  />
                ))}
                {categoricalColumns.length === 0 && (
                  <Typography variant="body2" color="text.secondary">
                    لا توجد أعمدة نصية
                  </Typography>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Divider sx={{ my: 3 }} />

      {/* توصيات */}
      <Box>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CheckCircle color="success" />
          التوصيات
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Alert severity="success" variant="outlined">
              <Typography variant="body2">
                ✅ <strong>جاهز للعرض:</strong> يمكنك الآن استعراض البيانات في الجدول أدناه
              </Typography>
            </Alert>
          </Grid>
          <Grid item xs={12} md={4}>
            <Alert severity="info" variant="outlined">
              <Typography variant="body2">
                📊 <strong>جاهز للرسومات:</strong> تم اختيار أفضل الأعمدة للرسم البياني تلقائياً
              </Typography>
            </Alert>
          </Grid>
          <Grid item xs={12} md={4}>
            <Alert severity="warning" variant="outlined">
              <Typography variant="body2">
                📈 <strong>جاهز للتحليل:</strong> راجع الإحصائيات التفصيلية في الأسفل
              </Typography>
            </Alert>
          </Grid>
        </Grid>
      </Box>
    </Paper>
  );
};

export default QuickAnalysisPanel;
