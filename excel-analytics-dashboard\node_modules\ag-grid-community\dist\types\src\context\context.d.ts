import type { AlignedGridsService } from '../alignedGrids/alignedGridsService';
import type { ApiFunctionService } from '../api/apiFunctionService';
import type { GridApi } from '../api/gridApi';
import type { ColumnAutosizeService } from '../columnAutosize/columnAutosizeService';
import type { ColumnAnimationService } from '../columnMove/columnAnimationService';
import type { ColumnMoveService } from '../columnMove/columnMoveService';
import type { ColumnResizeService } from '../columnResize/columnResizeService';
import type { ColumnDefFactory } from '../columns/columnDefFactory';
import type { ColumnFlexService } from '../columns/columnFlexService';
import type { ColumnGroupService } from '../columns/columnGroups/columnGroupService';
import type { ColumnHoverService } from '../columns/columnHover/columnHoverService';
import type { ColumnModel } from '../columns/columnModel';
import type { ColumnNameService } from '../columns/columnNameService';
import type { ColumnViewportService } from '../columns/columnViewportService';
import type { DataTypeService } from '../columns/dataTypeService';
import type { SelectionColService } from '../columns/selectionColService';
import type { VisibleColsService } from '../columns/visibleColsService';
import type { AgComponentUtils } from '../components/framework/agComponentUtils';
import type { FrameworkComponentWrapper } from '../components/framework/frameworkComponentWrapper';
import type { Registry } from '../components/framework/registry';
import type { UserComponentFactory } from '../components/framework/userComponentFactory';
import type { CtrlsService } from '../ctrlsService';
import type { DragAndDropService } from '../dragAndDrop/dragAndDropService';
import type { DragService } from '../dragAndDrop/dragService';
import type { HorizontalResizeService } from '../dragAndDrop/horizontalResizeService';
import type { RowDragService } from '../dragAndDrop/rowDragService';
import type { GridOptions } from '../entities/gridOptions';
import type { Environment } from '../environment';
import type { EventService } from '../eventService';
import type { AgGlobalEventListener } from '../events';
import type { ColumnFilterService } from '../filter/columnFilterService';
import type { FilterManager } from '../filter/filterManager';
import type { FilterValueService } from '../filter/filterValueService';
import type { QuickFilterService } from '../filter/quickFilterService';
import type { FocusService } from '../focusService';
import type { ScrollVisibleService } from '../gridBodyComp/scrollVisibleService';
import type { GridDestroyService } from '../gridDestroyService';
import type { GridOptionsService } from '../gridOptionsService';
import type { RowNodeBlockLoader } from '../infiniteRowModel/rowNodeBlockLoader';
import type { IChartService } from '../interfaces/IChartService';
import type { IRangeService } from '../interfaces/IRangeService';
import type { IRowDropHighlightService } from '../interfaces/IRowDropHighlightService';
import type { EditStrategyType } from '../interfaces/editStrategyType';
import type { IAdvancedFilterService } from '../interfaces/iAdvancedFilterService';
import type { IAggColumnNameService } from '../interfaces/iAggColumnNameService';
import type { IAggFuncService } from '../interfaces/iAggFuncService';
import type { IClientSideNodeManager } from '../interfaces/iClientSideNodeManager';
import type { IClipboardService } from '../interfaces/iClipboardService';
import type { IColsService } from '../interfaces/iColsService';
import type { IColumnCollectionService } from '../interfaces/iColumnCollectionService';
import type { IContextMenuService } from '../interfaces/iContextMenu';
import type { ICsvCreator } from '../interfaces/iCsvCreator';
import type { IEditModelService } from '../interfaces/iEditModelService';
import type { IEditService } from '../interfaces/iEditService';
import type { IExcelCreator } from '../interfaces/iExcelCreator';
import type { IExpansionService } from '../interfaces/iExpansionService';
import type { IFindService } from '../interfaces/iFind';
import type { IFooterService } from '../interfaces/iFooterService';
import type { IFrameworkOverrides } from '../interfaces/iFrameworkOverrides';
import type { IGroupFilterService } from '../interfaces/iGroupFilterService';
import type { IMenuFactory } from '../interfaces/iMenuFactory';
import type { IMultiFilterService } from '../interfaces/iMultiFilterService';
import type { IFilterPanelService, ISelectableFilterService } from '../interfaces/iNewFiltersToolPanel';
import type { IPinnedRowModel } from '../interfaces/iPinnedRowModel';
import type { IPivotColDefService } from '../interfaces/iPivotColDefService';
import type { IPivotResultColsService } from '../interfaces/iPivotResultColsService';
import type { IRowChildrenService } from '../interfaces/iRowChildrenService';
import type { IRowModel } from '../interfaces/iRowModel';
import type { IRowGroupStage, IRowNodeStage } from '../interfaces/iRowNodeStage';
import type { ISelectionService } from '../interfaces/iSelectionService';
import type { IServerSideTransactionManager } from '../interfaces/iServerSideRowModel';
import type { IShowRowGroupColsService } from '../interfaces/iShowRowGroupColsService';
import type { IShowRowGroupColsValueService } from '../interfaces/iShowRowGroupColsValueService';
import type { ISideBarService } from '../interfaces/iSideBar';
import type { IStickyRowService } from '../interfaces/iStickyRows';
import type { IMasterDetailService } from '../interfaces/masterDetail';
import type { IRenderStatusService } from '../interfaces/renderStatusService';
import type { IRowNumbersService } from '../interfaces/rowNumbers';
import type { AnimationFrameService } from '../misc/animationFrameService';
import type { ApiEventService } from '../misc/apiEvents/apiEventService';
import type { LocaleService } from '../misc/locale/localeService';
import type { MenuService } from '../misc/menu/menuService';
import type { StateService } from '../misc/state/stateService';
import type { TouchService } from '../misc/touchService';
import type { CellNavigationService } from '../navigation/cellNavigationService';
import type { HeaderNavigationService } from '../navigation/headerNavigationService';
import type { NavigationService } from '../navigation/navigationService';
import type { PageBoundsListener } from '../pagination/pageBoundsListener';
import type { PageBoundsService } from '../pagination/pageBoundsService';
import type { PaginationAutoPageSizeService } from '../pagination/paginationAutoPageSizeService';
import type { PaginationService } from '../pagination/paginationService';
import type { PinnedColumnService } from '../pinnedColumns/pinnedColumnService';
import type { AriaAnnouncementService } from '../rendering/ariaAnnouncementService';
import type { AutoWidthCalculator } from '../rendering/autoWidthCalculator';
import type { CellFlashService } from '../rendering/cell/cellFlashService';
import type { OverlayService } from '../rendering/overlays/overlayService';
import type { RowAutoHeightService } from '../rendering/row/rowAutoHeightService';
import type { RowContainerHeightService } from '../rendering/rowContainerHeightService';
import type { RowRenderer } from '../rendering/rowRenderer';
import type { RowSpanService } from '../rendering/spanning/rowSpanService';
import type { SpannedRowRenderer } from '../rendering/spanning/spannedRowRenderer';
import type { RowNodeSorter } from '../sort/rowNodeSorter';
import type { SortService } from '../sort/sortService';
import type { CellStyleService } from '../styling/cellStyleService';
import type { RowStyleService } from '../styling/rowStyleService';
import type { SyncService } from '../syncService';
import type { TooltipService } from '../tooltip/tooltipService';
import type { UndoRedoService } from '../undoRedo/undoRedoService';
import type { ValidationService } from '../validation/validationService';
import type { ExpressionService } from '../valueService/expressionService';
import type { ValueCache } from '../valueService/valueCache';
import type { ValueService } from '../valueService/valueService';
import type { PopupService } from '../widgets/popupService';
import type { GenericContextParams, GenericSingletonBean } from './genericContext';
import { GenericContext } from './genericContext';
export interface ContextParams extends GenericContextParams<BeanName, BeanCollection> {
    gridId: string;
    destroyCallback?: () => void;
}
export interface SingletonBean extends GenericSingletonBean<BeanName, BeanCollection> {
}
export type DynamicBeanName = 'detailCellRendererCtrl' | 'dndSourceComp' | 'fillHandle' | 'groupCellRendererCtrl' | 'headerFilterCellCtrl' | 'headerGroupCellCtrl' | 'rangeHandle' | 'tooltipFeature' | 'groupStrategy' | 'treeGroupStrategy' | EditStrategyType | 'rowNumberRowResizer' | 'agSetColumnFilterHandler' | 'agMultiColumnFilterHandler' | 'agGroupColumnFilterHandler' | 'agNumberColumnFilterHandler' | 'agDateColumnFilterHandler' | 'agTextColumnFilterHandler';
export type UserComponentName = 'agDragAndDropImage' | 'agColumnHeader' | 'agColumnGroupHeader' | 'agSortIndicator' | 'agAnimateShowChangeCellRenderer' | 'agAnimateSlideCellRenderer' | 'agLoadingCellRenderer' | 'agSkeletonCellRenderer' | 'agCheckboxCellRenderer' | 'agLoadingOverlay' | 'agNoRowsOverlay' | 'agTooltipComponent' | 'agReadOnlyFloatingFilter' | 'agTextColumnFilter' | 'agNumberColumnFilter' | 'agDateColumnFilter' | 'agDateInput' | 'agTextColumnFloatingFilter' | 'agNumberColumnFloatingFilter' | 'agDateColumnFloatingFilter' | 'agMultiColumnFilter' | 'agMultiColumnFloatingFilter' | 'agGroupColumnFilter' | 'agGroupColumnFloatingFilter' | 'agSetColumnFilter' | 'agSetColumnFloatingFilter' | 'agCellEditor' | 'agSelectCellEditor' | 'agTextCellEditor' | 'agNumberCellEditor' | 'agDateCellEditor' | 'agDateStringCellEditor' | 'agCheckboxCellEditor' | 'agLargeTextCellEditor' | 'agRichSelect' | 'agRichSelectCellEditor' | 'agMenuItem' | 'agColumnsToolPanel' | 'agFiltersToolPanel' | 'agNewFiltersToolPanel' | 'agGroupRowRenderer' | 'agGroupCellRenderer' | 'agDetailCellRenderer' | 'agSparklineCellRenderer' | 'agAggregationComponent' | 'agSelectedRowCountComponent' | 'agTotalRowCountComponent' | 'agFilteredRowCountComponent' | 'agTotalAndFilteredRowCountComponent' | 'agFindCellRenderer';
export type ClassImp = new (...args: []) => object;
interface ComponentMetaWithParams {
    classImp: ClassImp;
    /** Default params for provided components */
    params?: any;
    /** Update params for provided components before they are created */
    processParams?: ProcessParamsFunc;
}
interface ComponentMetaFunc {
    getComp: (beans: BeanCollection) => ClassImp | ComponentMetaWithParams;
}
export declare function isComponentMetaFunc(componentMeta: ClassImp | ComponentMetaWithParams | ComponentMetaFunc): componentMeta is ComponentMetaFunc;
export type ComponentMeta = ClassImp | ComponentMetaWithParams | ComponentMetaFunc;
export type ProcessParamsFunc<TParams = any> = (params: TParams) => TParams;
export interface CoreBeanCollection {
    context: Context;
    pageBoundsListener: PageBoundsListener;
    gos: GridOptionsService;
    environment: Environment;
    rowRenderer: RowRenderer;
    valueSvc: ValueService;
    eventSvc: EventService;
    colModel: ColumnModel;
    colViewport: ColumnViewportService;
    colNames: ColumnNameService;
    visibleCols: VisibleColsService;
    colMoves?: ColumnMoveService;
    colFlex?: ColumnFlexService;
    colResize?: ColumnResizeService;
    headerNavigation?: HeaderNavigationService;
    navigation?: NavigationService;
    colAnimation?: ColumnAnimationService;
    focusSvc: FocusService;
    popupSvc?: PopupService;
    cellStyles?: CellStyleService;
    colHover?: ColumnHoverService;
    userCompFactory: UserComponentFactory;
    registry: Registry;
    animationFrameSvc?: AnimationFrameService;
    dragSvc?: DragService;
    dragAndDrop?: DragAndDropService;
    sortSvc?: SortService;
    colFilter?: ColumnFilterService;
    filterManager?: FilterManager;
    rowContainerHeight: RowContainerHeightService;
    frameworkOverrides: IFrameworkOverrides;
    selectionSvc?: ISelectionService;
    rowStyleSvc?: RowStyleService;
    rowModel: IRowModel;
    ctrlsSvc: CtrlsService;
    valueCache?: ValueCache;
    localeSvc?: LocaleService;
    syncSvc: SyncService;
    ariaAnnounce: AriaAnnouncementService;
    rangeSvc?: IRangeService;
    validation?: ValidationService;
    gridApi: GridApi;
    gridOptions: GridOptions;
    eGridDiv: HTMLElement;
    pivotResultCols?: IPivotResultColsService;
    autoColSvc?: IColumnCollectionService;
    selectionColSvc?: SelectionColService;
    rowNumbersSvc?: IRowNumbersService;
    colDefFactory?: ColumnDefFactory;
    colAutosize?: ColumnAutosizeService;
    rowGroupColsSvc?: IColsService;
    valueColsSvc?: IColsService;
    pivotColsSvc?: IColsService;
    quickFilter?: QuickFilterService;
    showRowGroupCols?: IShowRowGroupColsService;
    showRowGroupColValueSvc?: IShowRowGroupColsValueService;
    dataTypeSvc?: DataTypeService;
    globalListener: AgGlobalEventListener;
    globalSyncListener: AgGlobalEventListener;
    stateSvc?: StateService;
    overlays?: OverlayService;
    pinnedRowModel?: IPinnedRowModel;
    menuSvc?: MenuService;
    apiEventSvc?: ApiEventService;
    undoRedo?: UndoRedoService;
    rowNodeBlockLoader?: RowNodeBlockLoader;
    csvCreator?: ICsvCreator;
    excelCreator?: IExcelCreator;
    clipboardSvc?: IClipboardService;
    cellNavigation?: CellNavigationService;
    scrollVisibleSvc: ScrollVisibleService;
    pinnedCols?: PinnedColumnService;
    expressionSvc?: ExpressionService;
    autoWidthCalc?: AutoWidthCalculator;
    agCompUtils?: AgComponentUtils;
    frameworkCompWrapper: FrameworkComponentWrapper;
    horizontalResizeSvc?: HorizontalResizeService;
    filterMenuFactory?: IMenuFactory;
    enterpriseMenuFactory?: IMenuFactory;
    contextMenuSvc?: IContextMenuService;
    editSvc?: IEditService;
    editModelSvc?: IEditModelService;
    alignedGridsSvc?: AlignedGridsService;
    paginationAutoPageSizeSvc?: PaginationAutoPageSizeService;
    pagination?: PaginationService;
    pageBounds: PageBoundsService;
    apiFunctionSvc: ApiFunctionService;
    gridDestroySvc: GridDestroyService;
    expansionSvc?: IExpansionService;
    sideBar?: ISideBarService;
    ssrmTxnManager?: IServerSideTransactionManager;
    aggFuncSvc?: IAggFuncService;
    advancedFilter: IAdvancedFilterService;
    filterStage?: IRowNodeStage;
    sortStage?: IRowNodeStage;
    flattenStage?: IRowNodeStage;
    groupStage?: IRowGroupStage;
    aggStage?: IRowNodeStage;
    pivotStage?: IRowNodeStage;
    filterAggStage?: IRowNodeStage;
    rowNodeSorter?: RowNodeSorter;
    pivotColDefSvc?: IPivotColDefService;
    chartSvc?: IChartService;
    aggColNameSvc?: IAggColumnNameService;
    renderStatus?: IRenderStatusService;
    rowDropHighlightSvc?: IRowDropHighlightService;
    rowDragSvc?: RowDragService;
    stickyRowSvc?: IStickyRowService;
    filterValueSvc?: FilterValueService;
    csrmNodeSvc?: IClientSideNodeManager;
    csrmChildrenTreeNodeSvc?: IClientSideNodeManager;
    cellFlashSvc?: CellFlashService;
    masterDetailSvc?: IMasterDetailService;
    tooltipSvc?: TooltipService;
    colGroupSvc?: ColumnGroupService;
    rowAutoHeight?: RowAutoHeightService;
    rowChildrenSvc?: IRowChildrenService;
    footerSvc?: IFooterService;
    touchSvc?: TouchService;
    rowSpanSvc?: RowSpanService;
    spannedRowRenderer?: SpannedRowRenderer;
    findSvc?: IFindService;
    groupFilter?: IGroupFilterService;
    multiFilter?: IMultiFilterService;
    filterPanelSvc?: IFilterPanelService;
    selectableFilter?: ISelectableFilterService;
}
export type BeanCollection = CoreBeanCollection & {
    [key in Exclude<BeanName, keyof CoreBeanCollection>]?: unknown;
};
export declare class Context extends GenericContext<BeanName, BeanCollection> {
    private gridId;
    private destroyCallback?;
    protected init(params: ContextParams): void;
    destroy(): void;
    getGridId(): string;
}
export type BeanName = 'advFilterExpSvc' | 'advancedFilter' | 'advSettingsMenuFactory' | 'aggFuncSvc' | 'agCompUtils' | 'aggColNameSvc' | 'aggStage' | 'alignedGridsSvc' | 'animationFrameSvc' | 'apiFunctionSvc' | 'ariaAnnounce' | 'apiEventSvc' | 'autoColSvc' | 'autoWidthCalc' | 'beans' | 'cellFlashSvc' | 'cellNavigation' | 'cellStyles' | 'changeDetectionSvc' | 'chartColSvc' | 'chartCrossFilterSvc' | 'chartMenuItemMapper' | 'chartMenuListFactory' | 'chartMenuSvc' | 'chartTranslation' | 'chartSvc' | 'agChartsExports' | 'clipboardSvc' | 'colAnimation' | 'colAutosize' | 'colChooserFactory' | 'colDefFactory' | 'colFilter' | 'colFlex' | 'colGroupSvc' | 'colHover' | 'colMenuFactory' | 'colModel' | 'colMoves' | 'colNames' | 'colResize' | 'colToolPanelFactory' | 'colViewport' | 'pivotResultCols' | 'context' | 'contextMenuSvc' | 'selectionColSvc' | 'ctrlsSvc' | 'csvCreator' | 'dataTypeSvc' | 'visibleCols' | 'dragAndDrop' | 'dragSvc' | 'editSvc' | 'editModelSvc' | 'excelCreator' | 'enterpriseMenuFactory' | 'environment' | 'eventSvc' | 'eGridDiv' | 'enterpriseChartProxyFactory' | 'expansionSvc' | 'expressionSvc' | 'filterAggStage' | 'filterManager' | 'filterMenuFactory' | 'filterPanelSvc' | 'filterStage' | 'filterValueSvc' | 'findSvc' | 'flashCellSvc' | 'flattenStage' | 'focusSvc' | 'footerSvc' | 'funcColsSvc' | 'rowNumbersSvc' | 'pivotColsSvc' | 'rowGroupColsSvc' | 'valueColsSvc' | 'frameworkCompWrapper' | 'frameworkOverrides' | 'globalListener' | 'globalSyncListener' | 'gridApi' | 'gridDestroySvc' | 'gridOptions' | 'gos' | 'gridOptionsWrapper' | 'gridSerializer' | 'groupFilter' | 'groupStage' | 'headerNavigation' | 'horizontalResizeSvc' | 'lazyBlockLoadingSvc' | 'licenseManager' | 'localeSvc' | 'masterDetailSvc' | 'menuItemMapper' | 'menuSvc' | 'menuUtils' | 'multiFilter' | 'navigation' | 'overlays' | 'paginationAutoPageSizeSvc' | 'pagination' | 'pinnedRowModel' | 'pinnedCols' | 'pivotColDefSvc' | 'pivotStage' | 'popupSvc' | 'quickFilter' | 'rangeSvc' | 'pageBoundsListener' | 'pageBounds' | 'registry' | 'renderStatus' | 'rowAutoHeight' | 'rowChildrenSvc' | 'rowContainerHeight' | 'rowDragSvc' | 'rowEditSvc' | 'rowModel' | 'rowNodeBlockLoader' | 'rowNodeSorter' | 'rowRenderer' | 'rowStyleSvc' | 'scrollVisibleSvc' | 'selectableFilter' | 'selectionController' | 'selectionSvc' | 'showRowGroupCols' | 'sideBar' | 'sortSvc' | 'sortStage' | 'ssrmBlockUtils' | 'ssrmExpandListener' | 'ssrmFilterListener' | 'ssrmListenerUtils' | 'ssrmNodeManager' | 'ssrmSortSvc' | 'ssrmStoreFactory' | 'ssrmStoreUtils' | 'ssrmTxnManager' | 'stateSvc' | 'statusBarSvc' | 'stickyRowSvc' | 'syncSvc' | 'tooltipSvc' | 'touchSvc' | 'undoRedo' | 'userCompFactory' | 'valueCache' | 'valueSvc' | 'validationLogger' | 'validation' | 'csrmNodeSvc' | 'csrmChildrenTreeNodeSvc' | 'rowSpanSvc' | 'spannedRowRenderer' | 'showRowGroupColValueSvc' | 'rowDropHighlightSvc';
export {};
