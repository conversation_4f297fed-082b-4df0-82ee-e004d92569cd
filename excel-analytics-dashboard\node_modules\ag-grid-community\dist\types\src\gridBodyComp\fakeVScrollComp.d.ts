import type { ComponentSelector } from '../widgets/component';
import { AbstractFakeScrollComp } from './abstractFakeScrollComp';
export declare class FakeVScrollComp extends AbstractFakeScrollComp {
    constructor();
    postConstruct(): void;
    protected setScrollVisible(): void;
    private onRowContainerHeightChanged;
    getScrollPosition(): number;
    setScrollPosition(value: number, force?: boolean): void;
}
export declare const FakeVScrollSelector: ComponentSelector;
