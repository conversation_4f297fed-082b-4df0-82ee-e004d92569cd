import React, { useState, useMemo } from 'react';
import { DataGrid } from '@mui/x-data-grid';
import {
  Paper,
  Typography,
  Box,
  TextField,
  Button,
  Stack,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  Download as DownloadIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';

const DataViewer = ({ data, columns, onDataFilter }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterColumn, setFilterColumn] = useState('');
  const [filterValue, setFilterValue] = useState('');
  const [pageSize, setPageSize] = useState(25);

  // Filter data based on search and filters
  const filteredData = useMemo(() => {
    let filtered = [...data];

    // Global search
    if (searchTerm) {
      filtered = filtered.filter(row =>
        Object.values(row).some(value =>
          String(value).toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Column-specific filter
    if (filterColumn && filterValue) {
      filtered = filtered.filter(row =>
        String(row[filterColumn]).toLowerCase().includes(filterValue.toLowerCase())
      );
    }

    // Notify parent component
    if (onDataFilter) {
      onDataFilter(filtered);
    }

    return filtered;
  }, [data, searchTerm, filterColumn, filterValue, onDataFilter]);

  const handleClearFilters = () => {
    setSearchTerm('');
    setFilterColumn('');
    setFilterValue('');
  };

  const handleExportData = () => {
    const csvContent = [
      columns.map(col => col.headerName).join(','),
      ...filteredData.map(row =>
        columns.map(col => `"${row[col.field] || ''}"`).join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'filtered_data.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Enhanced columns with better formatting
  const enhancedColumns = columns.map(col => ({
    ...col,
    renderCell: (params) => {
      const value = params.value;
      if (value === null || value === undefined || value === '') {
        return <span style={{ color: '#999', fontStyle: 'italic' }}>فارغ</span>;
      }
      
      // Check if it's a number
      if (!isNaN(value) && !isNaN(parseFloat(value))) {
        return <span style={{ fontWeight: 'bold', color: '#1976d2' }}>{value}</span>;
      }
      
      return <span>{value}</span>;
    }
  }));

  return (
    <Paper elevation={3} sx={{ p: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <VisibilityIcon color="primary" />
          عرض البيانات والفلترة
        </Typography>
        
        <Stack direction={{ xs: 'column', md: 'row' }} spacing={2} sx={{ mb: 2 }}>
          {/* Global Search */}
          <TextField
            label="البحث في جميع الأعمدة"
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'action.active' }} />
            }}
            sx={{ minWidth: 250 }}
          />
          
          {/* Column Filter */}
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel>اختر العمود للفلترة</InputLabel>
            <Select
              value={filterColumn}
              onChange={(e) => setFilterColumn(e.target.value)}
              label="اختر العمود للفلترة"
            >
              <MenuItem value="">
                <em>بدون فلترة</em>
              </MenuItem>
              {columns.map((col) => (
                <MenuItem key={col.field} value={col.field}>
                  {col.headerName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          {/* Filter Value */}
          {filterColumn && (
            <TextField
              label="قيمة الفلترة"
              variant="outlined"
              size="small"
              value={filterValue}
              onChange={(e) => setFilterValue(e.target.value)}
              sx={{ minWidth: 200 }}
            />
          )}
          
          {/* Action Buttons */}
          <Stack direction="row" spacing={1}>
            <Tooltip title="مسح جميع الفلاتر">
              <IconButton onClick={handleClearFilters} color="secondary">
                <ClearIcon />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="تصدير البيانات المفلترة">
              <IconButton onClick={handleExportData} color="primary">
                <DownloadIcon />
              </IconButton>
            </Tooltip>
          </Stack>
        </Stack>
        
        {/* Filter Status */}
        <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
          <Chip
            label={`إجمالي الصفوف: ${data.length}`}
            color="default"
            variant="outlined"
          />
          <Chip
            label={`الصفوف المفلترة: ${filteredData.length}`}
            color="primary"
            variant="outlined"
          />
          {(searchTerm || filterColumn) && (
            <Chip
              label="فلاتر نشطة"
              color="success"
              variant="filled"
              icon={<FilterIcon />}
            />
          )}
        </Stack>
      </Box>

      {/* Data Grid */}
      <Box sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={filteredData}
          columns={enhancedColumns}
          pageSize={pageSize}
          onPageSizeChange={(newPageSize) => setPageSize(newPageSize)}
          rowsPerPageOptions={[10, 25, 50, 100]}
          pagination
          disableSelectionOnClick
          density="comfortable"
          sx={{
            '& .MuiDataGrid-cell': {
              borderRight: '1px solid #e0e0e0',
            },
            '& .MuiDataGrid-columnHeaders': {
              backgroundColor: '#f5f5f5',
              fontWeight: 'bold',
            },
            '& .MuiDataGrid-row:hover': {
              backgroundColor: '#f0f7ff',
            }
          }}
          localeText={{
            noRowsLabel: 'لا توجد بيانات',
            noResultsOverlayLabel: 'لم يتم العثور على نتائج',
            errorOverlayDefaultLabel: 'حدث خطأ',
            toolbarColumns: 'الأعمدة',
            toolbarFilters: 'الفلاتر',
            toolbarDensity: 'الكثافة',
            toolbarExport: 'تصدير',
            columnsPanelTextFieldLabel: 'البحث عن عمود',
            columnsPanelShowAllButton: 'إظهار الكل',
            columnsPanelHideAllButton: 'إخفاء الكل',
            filterPanelAddFilter: 'إضافة فلتر',
            filterPanelDeleteIconLabel: 'حذف',
            filterOperatorContains: 'يحتوي على',
            filterOperatorEquals: 'يساوي',
            filterOperatorStartsWith: 'يبدأ بـ',
            filterOperatorEndsWith: 'ينتهي بـ',
            filterOperatorIsEmpty: 'فارغ',
            filterOperatorIsNotEmpty: 'غير فارغ',
            columnMenuLabel: 'قائمة',
            columnMenuShowColumns: 'إظهار الأعمدة',
            columnMenuFilter: 'فلترة',
            columnMenuHideColumn: 'إخفاء',
            columnMenuUnsort: 'إلغاء الترتيب',
            columnMenuSortAsc: 'ترتيب تصاعدي',
            columnMenuSortDesc: 'ترتيب تنازلي'
          }}
        />
      </Box>
    </Paper>
  );
};

export default DataViewer;
