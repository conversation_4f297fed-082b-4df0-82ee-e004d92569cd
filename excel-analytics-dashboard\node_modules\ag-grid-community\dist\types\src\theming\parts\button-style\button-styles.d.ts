import type { Part } from '../../Part';
import type { BorderValue, ColorValue, FontWeightValue, LengthValue } from '../../theme-types';
export type ButtonStyleParams = {
    /**
     * Text color of standard action buttons (e.g. "Reset" and "Apply")
     */
    buttonTextColor: ColorValue;
    /**
     * Font weight of standard action buttons (e.g. "Reset" and "Apply")
     */
    buttonFontWeight: FontWeightValue;
    /**
     * Background color of standard action buttons (e.g. "Reset" and "Apply")
     */
    buttonBackgroundColor: ColorValue;
    /**
     * Border around standard action buttons (e.g. "Reset" and "Apply")
     */
    buttonBorder: BorderValue;
    /**
     * Corner radius of standard action buttons (e.g. "Reset" and "Apply")
     */
    buttonBorderRadius: LengthValue;
    /**
     * Horizontal padding inside standard action buttons (e.g. "Reset" and "Apply")
     */
    buttonHorizontalPadding: LengthValue;
    /**
     * Vertical padding inside standard action buttons (e.g. "Reset" and "Apply")
     */
    buttonVerticalPadding: LengthValue;
    /**
     * Text color of standard action buttons (e.g. "Reset" and "Apply") when hovered
     */
    buttonHoverTextColor: ColorValue;
    /**
     * Background color of standard action buttons (e.g. "Reset" and "Apply") when hovered
     */
    buttonHoverBackgroundColor: ColorValue;
    /**
     * Border around standard action buttons (e.g. "Reset" and "Apply") when hovered. Only has an effect if a border is enabled with `buttonBorder`.
     */
    buttonHoverBorder: BorderValue;
    /**
     * Text color of standard action buttons (e.g. "Reset" and "Apply") when being clicked
     */
    buttonActiveTextColor: ColorValue;
    /**
     * Background color of standard action buttons (e.g. "Reset" and "Apply") when being clicked
     */
    buttonActiveBackgroundColor: ColorValue;
    /**
     * Border around standard action buttons (e.g. "Reset" and "Apply") when being clicked.
     */
    buttonActiveBorder: BorderValue;
    /**
     * Text color of standard action buttons (e.g. "Reset" and "Apply") when disabled
     */
    buttonDisabledTextColor: ColorValue;
    /**
     * Background color of standard action buttons (e.g. "Reset" and "Apply") when disabled
     */
    buttonDisabledBackgroundColor: ColorValue;
    /**
     * Border around standard action buttons (e.g. "Reset" and "Apply") when disabled.
     */
    buttonDisabledBorder: BorderValue;
};
export declare const buttonStyleBase: Part<ButtonStyleParams>;
export declare const buttonStyleQuartz: Part<ButtonStyleParams>;
export declare const buttonStyleAlpine: Part<ButtonStyleParams>;
export declare const buttonStyleBalham: Part<ButtonStyleParams>;
