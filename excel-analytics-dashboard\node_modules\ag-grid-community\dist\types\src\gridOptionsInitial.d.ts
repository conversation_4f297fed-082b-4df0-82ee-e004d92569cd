import type { GridOptions } from './entities/gridOptions';
export declare const INITIAL_GRID_OPTION_KEYS: {
    enableBrowserTooltips: boolean;
    tooltipTrigger: boolean;
    tooltipMouseTrack: boolean;
    tooltipShowMode: boolean;
    tooltipInteraction: boolean;
    defaultColGroupDef: boolean;
    suppressAutoSize: boolean;
    skipHeaderOnAutoSize: boolean;
    autoSizeStrategy: boolean;
    components: boolean;
    stopEditingWhenCellsLoseFocus: boolean;
    undoRedoCellEditing: boolean;
    undoRedoCellEditingLimit: boolean;
    excelStyles: boolean;
    cacheQuickFilter: boolean;
    customChartThemes: boolean;
    chartThemeOverrides: boolean;
    chartToolPanelsDef: boolean;
    loadingCellRendererSelector: boolean;
    localeText: boolean;
    keepDetailRows: boolean;
    keepDetailRowsCount: boolean;
    detailRowHeight: boolean;
    detailRowAutoHeight: boolean;
    tabIndex: boolean;
    valueCache: boolean;
    valueCacheNeverExpires: boolean;
    enableCellExpressions: boolean;
    suppressTouch: boolean;
    suppressBrowserResizeObserver: boolean;
    suppressPropertyNamesCheck: boolean;
    debug: boolean;
    dragAndDropImageComponent: boolean;
    loadingOverlayComponent: boolean;
    suppressLoadingOverlay: boolean;
    noRowsOverlayComponent: boolean;
    paginationPageSizeSelector: boolean;
    paginateChildRows: boolean;
    pivotPanelShow: boolean;
    pivotSuppressAutoColumn: boolean;
    suppressExpandablePivotGroups: boolean;
    aggFuncs: boolean;
    allowShowChangeAfterFilter: boolean;
    ensureDomOrder: boolean;
    enableRtl: boolean;
    suppressColumnVirtualisation: boolean;
    suppressMaxRenderedRowRestriction: boolean;
    suppressRowVirtualisation: boolean;
    rowDragText: boolean;
    groupLockGroupColumns: boolean;
    suppressGroupRowsSticky: boolean;
    rowModelType: boolean;
    cacheOverflowSize: boolean;
    infiniteInitialRowCount: boolean;
    serverSideInitialRowCount: boolean;
    maxBlocksInCache: boolean;
    maxConcurrentDatasourceRequests: boolean;
    blockLoadDebounceMillis: boolean;
    serverSideOnlyRefreshFilteredGroups: boolean;
    serverSidePivotResultFieldSeparator: boolean;
    viewportRowModelPageSize: boolean;
    viewportRowModelBufferSize: boolean;
    debounceVerticalScrollbar: boolean;
    suppressAnimationFrame: boolean;
    suppressPreventDefaultOnMouseWheel: boolean;
    scrollbarWidth: boolean;
    icons: boolean;
    suppressRowTransform: boolean;
    gridId: boolean;
    enableGroupEdit: boolean;
    initialState: boolean;
    processUnpinnedColumns: boolean;
    createChartContainer: boolean;
    getLocaleText: boolean;
    getRowId: boolean;
    reactiveCustomComponents: boolean;
    renderingMode: boolean;
    columnMenu: boolean;
    suppressSetFilterByDefault: boolean;
    getDataPath: boolean;
    enableCellSpan: boolean;
    enableFilterHandlers: boolean;
    filterHandlers: boolean;
};
type InitialGridOptionKey = keyof typeof INITIAL_GRID_OPTION_KEYS;
export type ManagedGridOptionKey = Exclude<keyof GridOptions, InitialGridOptionKey>;
export type ManagedGridOptions<TData = any> = {
    [K in ManagedGridOptionKey]?: GridOptions<TData>[K];
};
export {};
