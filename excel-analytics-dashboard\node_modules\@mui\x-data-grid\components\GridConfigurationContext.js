"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GridConfigurationContext = void 0;
var React = _interopRequireWildcard(require("react"));
const GridConfigurationContext = exports.GridConfigurationContext = /*#__PURE__*/React.createContext(undefined);
if (process.env.NODE_ENV !== "production") GridConfigurationContext.displayName = "GridConfigurationContext";