export declare const colorSchemeLight: import("../../Part").Part<{
    readonly backgroundColor: import("../../theme-types").ColorValue;
    readonly foregroundColor: import("../../theme-types").ColorValue;
    readonly borderColor: import("../../theme-types").ColorValue;
    readonly chromeBackgroundColor: import("../../theme-types").ColorValue;
    readonly browserColorScheme: import("../../theme-types").ColorSchemeValue;
}>;
export declare const colorSchemeLightWarm: import("../../Part").Part<{
    foregroundColor: import("../../theme-types").ColorValue;
    borderColor: import("../../theme-types").ColorValue;
    chromeBackgroundColor: import("../../theme-types").ColorValue;
    backgroundColor: import("../../theme-types").ColorValue;
    browserColorScheme: import("../../theme-types").ColorSchemeValue;
}>;
export declare const colorSchemeLightCold: import("../../Part").Part<{
    foregroundColor: import("../../theme-types").ColorValue;
    chromeBackgroundColor: import("../../theme-types").ColorValue;
    backgroundColor: import("../../theme-types").ColorValue;
    borderColor: import("../../theme-types").ColorValue;
    browserColorScheme: import("../../theme-types").ColorSchemeValue;
}>;
export declare const colorSchemeDark: import("../../Part").Part<{
    readonly backgroundColor: import("../../theme-types").ColorValue;
    readonly foregroundColor: import("../../theme-types").ColorValue;
    readonly chromeBackgroundColor: import("../../theme-types").ColorValue;
    readonly rowHoverColor: import("../../theme-types").ColorValue;
    readonly selectedRowBackgroundColor: import("../../theme-types").ColorValue;
    readonly menuBackgroundColor: import("../../theme-types").ColorValue;
    readonly browserColorScheme: import("../../theme-types").ColorSchemeValue;
    readonly popupShadow: import("../../theme-types").ShadowValue;
    readonly cardShadow: import("../../theme-types").ShadowValue;
    readonly advancedFilterBuilderJoinPillColor: import("../../theme-types").ColorValue;
    readonly advancedFilterBuilderColumnPillColor: import("../../theme-types").ColorValue;
    readonly advancedFilterBuilderOptionPillColor: import("../../theme-types").ColorValue;
    readonly advancedFilterBuilderValuePillColor: import("../../theme-types").ColorValue;
    readonly filterPanelApplyButtonColor: import("../../theme-types").ColorValue;
    readonly findMatchColor: import("../../theme-types").ColorValue;
    readonly findActiveMatchColor: import("../../theme-types").ColorValue;
    readonly checkboxUncheckedBorderColor: import("../../theme-types").ColorValue;
    readonly toggleButtonOffBackgroundColor: import("../../theme-types").ColorValue;
    readonly rowBatchEditBackgroundColor: import("../../theme-types").ColorValue;
    readonly cellBatchEditBackgroundColor: import("../../theme-types").ColorValue;
    readonly cellBatchEditTextColor: import("../../theme-types").ColorValue;
    readonly rowBatchEditTextColor: import("../../theme-types").ColorValue;
    readonly borderColor: import("../../theme-types").ColorValue;
}>;
export declare const colorSchemeDarkWarm: import("../../Part").Part<{
    backgroundColor: import("../../theme-types").ColorValue;
    foregroundColor: import("../../theme-types").ColorValue;
    browserColorScheme: import("../../theme-types").ColorSchemeValue;
}>;
export declare const colorSchemeDarkBlue: import("../../Part").Part<{
    readonly backgroundColor: import("../../theme-types").ColorValue;
    readonly foregroundColor: import("../../theme-types").ColorValue;
    readonly chromeBackgroundColor: import("../../theme-types").ColorValue;
    readonly rowHoverColor: import("../../theme-types").ColorValue;
    readonly selectedRowBackgroundColor: import("../../theme-types").ColorValue;
    readonly menuBackgroundColor: import("../../theme-types").ColorValue;
    readonly browserColorScheme: import("../../theme-types").ColorSchemeValue;
    readonly popupShadow: import("../../theme-types").ShadowValue;
    readonly cardShadow: import("../../theme-types").ShadowValue;
    readonly advancedFilterBuilderJoinPillColor: import("../../theme-types").ColorValue;
    readonly advancedFilterBuilderColumnPillColor: import("../../theme-types").ColorValue;
    readonly advancedFilterBuilderOptionPillColor: import("../../theme-types").ColorValue;
    readonly advancedFilterBuilderValuePillColor: import("../../theme-types").ColorValue;
    readonly filterPanelApplyButtonColor: import("../../theme-types").ColorValue;
    readonly findMatchColor: import("../../theme-types").ColorValue;
    readonly findActiveMatchColor: import("../../theme-types").ColorValue;
    readonly checkboxUncheckedBorderColor: import("../../theme-types").ColorValue;
    readonly toggleButtonOffBackgroundColor: import("../../theme-types").ColorValue;
    readonly rowBatchEditBackgroundColor: import("../../theme-types").ColorValue;
    readonly cellBatchEditBackgroundColor: import("../../theme-types").ColorValue;
    readonly cellBatchEditTextColor: import("../../theme-types").ColorValue;
    readonly rowBatchEditTextColor: import("../../theme-types").ColorValue;
    readonly borderColor: import("../../theme-types").ColorValue;
}>;
export declare const colorSchemeVariable: import("../../Part").Part<{
    readonly backgroundColor: import("../../theme-types").ColorValue;
    readonly foregroundColor: import("../../theme-types").ColorValue;
    readonly borderColor: import("../../theme-types").ColorValue;
    readonly chromeBackgroundColor: import("../../theme-types").ColorValue;
    readonly browserColorScheme: import("../../theme-types").ColorSchemeValue;
}>;
