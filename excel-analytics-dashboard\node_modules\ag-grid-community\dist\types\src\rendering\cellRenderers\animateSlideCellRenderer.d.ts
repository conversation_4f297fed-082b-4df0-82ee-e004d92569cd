import { Component } from '../../widgets/component';
import type { ICellRenderer } from './iCellRenderer';
export declare class AnimateSlideCellRenderer extends Component implements ICellRenderer {
    private eCurrent;
    private ePrevious;
    private lastValue;
    private refreshCount;
    constructor();
    init(params: any): void;
    addSlideAnimation(): void;
    refresh(params: any, isInitialRender?: boolean): boolean;
}
