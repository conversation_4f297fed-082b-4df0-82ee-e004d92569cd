import { BeanStub } from '../../../context/beanStub';
import type { AgColumnGroup } from '../../../entities/agColumnGroup';
import type { IHeaderGroupCellComp } from './headerGroupCellCtrl';
export declare class GroupWidthFeature extends BeanStub {
    private columnGroup;
    private comp;
    private removeChildListenersFuncs;
    constructor(comp: IHeaderGroupCellComp, columnGroup: AgColumnGroup);
    postConstruct(): void;
    private addListenersToChildrenColumns;
    private removeListenersOnChildrenColumns;
    private onDisplayedChildrenChanged;
    private onWidthChanged;
}
