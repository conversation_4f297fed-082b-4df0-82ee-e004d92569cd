import type { AgColumn } from '../entities/agColumn';
import type { AgProvidedColumnGroup } from '../entities/agProvidedColumnGroup';
import type { GridOptionsService } from '../gridOptionsService';
export declare function placeLockedColumns(cols: AgColumn[], gos: GridOptionsService): AgColumn[];
export declare function doesMovePassMarryChildren(allColumnsCopy: AgColumn[], gridBalancedTree: (AgColumn | AgProvidedColumnGroup)[]): boolean;
