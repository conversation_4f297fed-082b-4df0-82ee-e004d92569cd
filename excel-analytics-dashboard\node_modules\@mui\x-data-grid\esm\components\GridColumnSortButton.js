import _objectWithoutPropertiesLoose from "@babel/runtime/helpers/esm/objectWithoutPropertiesLoose";
import _extends from "@babel/runtime/helpers/esm/extends";
const _excluded = ["direction", "index", "sortingOrder", "disabled", "className"];
import * as React from 'react';
import PropTypes from 'prop-types';
import composeClasses from '@mui/utils/composeClasses';
import { styled } from '@mui/system';
import clsx from 'clsx';
import { useGridApiContext } from "../hooks/utils/useGridApiContext.js";
import { getDataGridUtilityClass } from "../constants/gridClasses.js";
import { useGridRootProps } from "../hooks/utils/useGridRootProps.js";
import { vars } from "../constants/cssVariables.js";
import { GridColumnUnsortedIcon } from "./GridColumnUnsortedIcon.js";
import { NotRendered } from "../utils/assert.js";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
const useUtilityClasses = ownerState => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ['sortButton'],
    icon: ['sortIcon']
  };
  return composeClasses(slots, getDataGridUtilityClass, classes);
};
const GridColumnSortButtonRoot = styled(NotRendered, {
  name: 'MuiDataGrid',
  slot: 'SortButton'
})({
  transition: vars.transition(['opacity'], {
    duration: vars.transitions.duration.short,
    easing: vars.transitions.easing.easeInOut
  })
});
function getIcon(icons, direction, className, sortingOrder) {
  let Icon;
  const iconProps = {};
  if (direction === 'asc') {
    Icon = icons.columnSortedAscendingIcon;
  } else if (direction === 'desc') {
    Icon = icons.columnSortedDescendingIcon;
  } else {
    Icon = GridColumnUnsortedIcon;
    iconProps.sortingOrder = sortingOrder;
  }
  return Icon ? /*#__PURE__*/_jsx(Icon, _extends({
    fontSize: "small",
    className: className
  }, iconProps)) : null;
}
function GridColumnSortButton(props) {
  const {
      direction,
      index,
      sortingOrder,
      disabled,
      className
    } = props,
    other = _objectWithoutPropertiesLoose(props, _excluded);
  const apiRef = useGridApiContext();
  const rootProps = useGridRootProps();
  const ownerState = _extends({}, props, {
    classes: rootProps.classes
  });
  const classes = useUtilityClasses(ownerState);
  const iconElement = getIcon(rootProps.slots, direction, classes.icon, sortingOrder);
  if (!iconElement) {
    return null;
  }
  const iconButton = /*#__PURE__*/_jsx(GridColumnSortButtonRoot, _extends({
    as: rootProps.slots.baseIconButton,
    ownerState: ownerState,
    "aria-label": apiRef.current.getLocaleText('columnHeaderSortIconLabel'),
    title: apiRef.current.getLocaleText('columnHeaderSortIconLabel'),
    size: "small",
    disabled: disabled,
    className: clsx(classes.root, className)
  }, rootProps.slotProps?.baseIconButton, other, {
    children: iconElement
  }));
  return /*#__PURE__*/_jsxs(React.Fragment, {
    children: [index != null && /*#__PURE__*/_jsx(rootProps.slots.baseBadge, {
      badgeContent: index,
      color: "default",
      overlap: "circular",
      children: iconButton
    }), index == null && iconButton]
  });
}
process.env.NODE_ENV !== "production" ? GridColumnSortButton.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  direction: PropTypes.oneOf(['asc', 'desc']),
  disabled: PropTypes.bool,
  field: PropTypes.string.isRequired,
  index: PropTypes.number,
  onClick: PropTypes.func,
  sortingOrder: PropTypes.arrayOf(PropTypes.oneOf(['asc', 'desc'])).isRequired
} : void 0;
export { GridColumnSortButton };