import type { Named<PERSON><PERSON> } from '../context/bean';
import { BeanStub } from '../context/beanStub';
import type { BeanCollection } from '../context/context';
import type { AgEventType } from '../eventTypes';
import type { BatchEditingEvent, CellFocusedEvent } from '../events';
import type { CellRange } from '../interfaces/IRangeService';
import type { EditingCellPosition, ICellEditorParams, ICellEditorValidationError } from '../interfaces/iCellEditor';
import type { RefreshCellsParams } from '../interfaces/iCellsParams';
import type { EditMap, GetEditsParams } from '../interfaces/iEditModelService';
import type { EditNavOnValidationResult, EditPosition, EditRowPosition, EditSource, IEditService, IsEditingParams, StartEditParams, StopEditParams, _SetEditingCellsParams } from '../interfaces/iEditService';
import type { IRowNode } from '../interfaces/iRowNode';
import type { IRowStyleFeature } from '../interfaces/iRowStyleFeature';
import type { UserCompDetails } from '../interfaces/iUserCompDetails';
import { CellCtrl } from '../rendering/cell/cellCtrl';
import type { RowCtrl } from '../rendering/row/rowCtrl';
import { PopupEditorWrapper } from './cellEditors/popupEditorWrapper';
import { CellEditStyleFeature } from './styles/cellEditStyleFeature';
type BatchPrepDetails = {
    compDetails?: UserCompDetails;
    valueToDisplay?: any;
};
export declare class EditService extends BeanStub implements NamedBean, IEditService {
    beanName: "editSvc";
    private batch;
    private model;
    private valueSvc;
    private rangeSvc;
    private strategy?;
    postConstruct(): void;
    isBatchEditing(): boolean;
    setBatchEditing(enabled: boolean): void;
    private createStrategy;
    private destroyStrategy;
    shouldStartEditing(position: Required<EditPosition>, event?: KeyboardEvent | MouseEvent | null, cellStartedEdit?: boolean | null, source?: EditSource): boolean | null;
    shouldStopEditing(position?: EditPosition, event?: KeyboardEvent | MouseEvent | null | undefined, source?: EditSource): boolean | null;
    shouldCancelEditing(position?: EditPosition, event?: KeyboardEvent | MouseEvent | null | undefined, source?: EditSource): boolean | null;
    validateEdit(): ICellEditorValidationError[] | null;
    isEditing(position?: EditPosition, params?: IsEditingParams): boolean;
    isRowEditing(rowNode?: IRowNode, params?: IsEditingParams): boolean;
    /** @returns whether to prevent default on event */
    startEditing(position: Required<EditPosition>, params: StartEditParams): void;
    stopEditing(position?: EditPosition, params?: StopEditParams): boolean;
    private navigateAfterEdit;
    private processEdits;
    private setNodeDataValue;
    setEditMap(edits: EditMap, params?: _SetEditingCellsParams): void;
    private dispatchEditValuesChanged;
    bulkRefresh(position?: EditPosition, editMap?: EditMap, params?: RefreshCellsParams): void;
    private refCell;
    stopAllEditing(cancel?: boolean, source?: 'api' | 'ui'): void;
    isCellEditable(position: Required<EditPosition>, source?: 'api' | 'ui'): boolean;
    cellEditingInvalidCommitBlocks(): boolean;
    checkNavWithValidation(position?: EditPosition, event?: Event | CellFocusedEvent): EditNavOnValidationResult;
    revertSingleCellEdit(cellPosition: Required<EditPosition>, focus?: boolean): void;
    hasValidationErrors(position?: EditPosition): boolean;
    moveToNextCell(prev: CellCtrl | RowCtrl, backwards: boolean, event?: KeyboardEvent, source?: 'api' | 'ui'): boolean | null;
    getCellDataValue({ rowNode, column }: Required<EditPosition>): any;
    getRowDataValue(rowNode: IRowNode, params?: GetEditsParams | undefined): any;
    addStopEditingWhenGridLosesFocus(viewports: HTMLElement[]): void;
    createPopupEditorWrapper(params: ICellEditorParams): PopupEditorWrapper;
    setDataValue(position: Required<EditPosition>, newValue: any, eventSource?: string): boolean | undefined;
    handleColDefChanged(cellCtrl: CellCtrl): void;
    destroy(): void;
    prepDetailsDuringBatch(position: Required<EditPosition>, params: BatchPrepDetails): BatchPrepDetails | undefined;
    cleanupEditors(): void;
    dispatchCellEvent<T extends AgEventType>(position: Required<EditPosition>, event?: Event | null, type?: T, payload?: any): void;
    dispatchRowEvent(position: Required<EditRowPosition>, type: 'rowEditingStarted' | 'rowEditingStopped'): void;
    dispatchBatchEvent(type: 'batchEditingStarted' | 'batchEditingStopped', edits: EditMap): void;
    createBatchEditEvent<T extends AgEventType>(type: T, edits: EditMap): BatchEditingEvent<T>;
    applyBulkEdit({ rowNode, column }: Required<EditPosition>, ranges: CellRange[]): void;
    createCellStyleFeature(cellCtrl: CellCtrl, beans: BeanCollection): CellEditStyleFeature;
    createRowStyleFeature(rowCtrl: RowCtrl, beans: BeanCollection): IRowStyleFeature;
    setEditingCells(cells: EditingCellPosition[], params?: _SetEditingCellsParams): void;
    onCellFocused(event: CellFocusedEvent): void;
    allowedFocusTargetOnValidation(cellPosition: EditPosition): CellCtrl | undefined;
}
export {};
