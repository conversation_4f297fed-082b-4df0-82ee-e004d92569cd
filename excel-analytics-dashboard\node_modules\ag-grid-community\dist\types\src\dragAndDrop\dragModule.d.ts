import type { _DragGridA<PERSON> } from '../api/gridApi';
import type { _ModuleWithApi, _ModuleWithoutApi } from '../interfaces/iModule';
/**
 * @internal
 */
export declare const DragModule: _ModuleWithoutApi;
/**
 * @feature Import & Export -> Drag & Drop
 * @colDef dndSource, dndSourceOnRowDrag
 */
export declare const DragAndDropModule: _ModuleWithoutApi;
/**
 * @internal
 */
export declare const SharedDragAndDropModule: _ModuleWithoutApi;
/**
 * @feature Rows -> Row Dragging
 * @colDef rowDrag
 */
export declare const RowDragModule: _ModuleWithApi<_DragGridApi<any>>;
/**
 * @internal
 */
export declare const HorizontalResizeModule: _ModuleWithoutApi;
