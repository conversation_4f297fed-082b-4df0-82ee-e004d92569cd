import type { BeanCollection } from '../context/context';
import type { RowDropPositionIndicator, SetRowDropPositionIndicatorParams } from '../interfaces/IRowDropHighlightService';
import type { RowDropZoneEvents, RowDropZoneParams } from './rowDragFeature';
export declare function addRowDropZone(beans: BeanCollection, params: RowDropZoneParams): void;
export declare function removeRowDropZone(beans: BeanCollection, params: RowDropZoneParams): void;
export declare function getRowDropZoneParams(beans: BeanCollection, events?: RowDropZoneEvents): RowDropZoneParams | undefined;
export declare function getRowDropPositionIndicator(beans: BeanCollection): RowDropPositionIndicator;
export declare function setRowDropPositionIndicator<TData>(beans: BeanCollection, params: SetRowDropPositionIndicatorParams<TData>): void;
