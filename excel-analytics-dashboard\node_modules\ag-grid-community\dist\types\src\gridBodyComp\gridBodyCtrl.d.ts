import { BeanStub } from '../context/beanStub';
import type { BeanCollection } from '../context/context';
import type { LayoutView } from '../styling/layoutFeature';
import { GridBodyScrollFeature } from './gridBodyScrollFeature';
export type RowAnimationCssClasses = 'ag-row-animation' | 'ag-row-no-animation';
export declare const CSS_CLASS_FORCE_VERTICAL_SCROLL = "ag-force-vertical-scroll";
export interface IGridBodyComp extends LayoutView {
    setColumnMovingCss(cssClass: string, on: boolean): void;
    setCellSelectableCss(cssClass: string | null, on: boolean): void;
    setTopHeight(height: number): void;
    setTopInvisible(invisible: boolean): void;
    setBottomHeight(height: number): void;
    setBottomInvisible(invisible: boolean): void;
    setStickyTopHeight(height: string): void;
    setStickyTopTop(offsetTop: string): void;
    setStickyTopWidth(width: string): void;
    setStickyBottomHeight(height: string): void;
    setStickyBottomBottom(offsetBottom: string): void;
    setStickyBottomWidth(width: string): void;
    setColumnCount(count: number): void;
    setRowCount(count: number): void;
    setRowAnimationCssOnBodyViewport(cssClass: RowAnimationCssClasses, animate: boolean): void;
    setAlwaysVerticalScrollClass(cssClass: string | null, on: boolean): void;
    setPinnedTopBottomOverflowY(overflow: 'scroll' | 'hidden'): void;
    registerBodyViewportResizeListener(listener: () => void): void;
    setBodyViewportWidth(width: string): void;
    setGridRootRole(role: 'grid' | 'treegrid'): void;
}
export declare class GridBodyCtrl extends BeanStub {
    private ctrlsSvc;
    private colModel;
    private scrollVisibleSvc;
    private rowGroupColsSvc?;
    private pinnedRowModel?;
    private filterManager?;
    wireBeans(beans: BeanCollection): void;
    private comp;
    eGridBody: HTMLElement;
    eBodyViewport: HTMLElement;
    private eTop;
    private eBottom;
    private eStickyTop;
    private eCenterColsViewport;
    private eFullWidthContainer;
    private eStickyTopFullWidthContainer;
    private eStickyBottomFullWidthContainer;
    stickyTopHeight: number;
    private eStickyBottom;
    stickyBottomHeight: number;
    scrollFeature: GridBodyScrollFeature;
    setComp(comp: IGridBodyComp, eGridBody: HTMLElement, eBodyViewport: HTMLElement, eTop: HTMLElement, eBottom: HTMLElement, eStickyTop: HTMLElement, eStickyBottom: HTMLElement): void;
    private addEventListeners;
    private toggleRowResizeStyles;
    private onGridColumnsChanged;
    private onScrollVisibilityChanged;
    private setGridRootRole;
    private addFocusListeners;
    setColumnMovingCss(moving: boolean): void;
    setCellTextSelection(selectable?: boolean): void;
    private updateScrollingClasses;
    private disableBrowserDragging;
    private addStopEditingWhenGridLosesFocus;
    updateRowCount(): void;
    registerBodyViewportResizeListener(listener: () => void): void;
    setVerticalScrollPaddingVisible(visible: boolean): void;
    isVerticalScrollShowing(): boolean;
    private setupRowAnimationCssClass;
    private addBodyViewportListener;
    private addFullWidthContainerWheelListener;
    private onFullWidthContainerWheel;
    private onStickyWheel;
    private onHorizontalWheel;
    private scrollGridBodyToMatchEvent;
    private onBodyViewportContextMenu;
    private onBodyViewportWheel;
    scrollVertically(pixels: number): number;
    private setFloatingHeights;
    setStickyTopHeight(height?: number): void;
    setStickyBottomHeight(height?: number): void;
    private setStickyWidth;
    private setStickyTopOffsetTop;
    private setStickyBottomOffsetBottom;
}
