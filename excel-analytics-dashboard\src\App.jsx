import React, { useState } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Container, AppBar, Toolbar, Typography, Box } from '@mui/material';
import ExcelUploader from './components/ExcelUploader';
import DataViewer from './components/DataViewer';
import ChartDashboard from './components/ChartDashboard';
import StatisticsPanel from './components/StatisticsPanel';
import QuickAnalysisPanel from './components/QuickAnalysisPanel';
import './App.css';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
    },
  },
});

function App() {
  const [excelData, setExcelData] = useState(null);
  const [filteredData, setFilteredData] = useState(null);
  const [columns, setColumns] = useState([]);
  const [autoAnalysis, setAutoAnalysis] = useState(null);

  const handleDataUpload = (data, cols) => {
    console.log('تم رفع البيانات:', data.length, 'صف،', cols.length, 'عمود');

    setExcelData(data);
    setFilteredData(data);
    setColumns(cols);

    // التحليل التلقائي للبيانات
    performAutoAnalysis(data, cols);

    // التمرير التلقائي إلى منطقة التحليل بعد ثانيتين
    setTimeout(() => {
      const analysisElement = document.getElementById('analysis-section');
      if (analysisElement) {
        analysisElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 2000);
  };

  const handleDataFilter = (filtered) => {
    setFilteredData(filtered);
  };

  const performAutoAnalysis = (data, cols) => {
    console.log('بدء التحليل التلقائي...');

    // تحديد الأعمدة الرقمية والنصية تلقائياً
    const numericColumns = [];
    const categoricalColumns = [];

    cols.forEach(col => {
      const sampleValues = data.slice(0, 10).map(row => row[col.field]);
      const numericValues = sampleValues.filter(val => !isNaN(val) && !isNaN(parseFloat(val)) && val !== '');

      if (numericValues.length >= sampleValues.length * 0.7) {
        numericColumns.push(col);
      } else {
        categoricalColumns.push(col);
      }
    });

    console.log('الأعمدة الرقمية:', numericColumns.map(c => c.headerName));
    console.log('الأعمدة النصية:', categoricalColumns.map(c => c.headerName));

    // اختيار أفضل أعمدة للرسم البياني
    const bestXColumn = categoricalColumns.length > 0 ? categoricalColumns[0].field : cols[0]?.field;
    const bestYColumn = numericColumns.length > 0 ? numericColumns[0].field : cols[1]?.field;

    setAutoAnalysis({
      numericColumns,
      categoricalColumns,
      recommendedXColumn: bestXColumn,
      recommendedYColumn: bestYColumn,
      totalRows: data.length,
      totalColumns: cols.length
    });

    console.log('التحليل التلقائي مكتمل');
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AppBar position="static" elevation={2}>
        <Toolbar>
          <Typography variant="h4" component="div" sx={{ flexGrow: 1, textAlign: 'center' }}>
            📊 نظام تحليل البيانات المتقدم - Excel Analytics Dashboard
          </Typography>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4 }}>
          <ExcelUploader onDataUpload={handleDataUpload} />
        </Box>

        {!excelData && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h4" color="text.secondary" gutterBottom>
              🚀 مرحباً بك في نظام تحليل البيانات المتقدم
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
              ارفع ملف Excel أو CSV لبدء التحليل التلقائي والحصول على رسومات بيانية متقدمة
            </Typography>
            <Typography variant="body1" color="text.secondary">
              ✨ سيتم تحليل البيانات تلقائياً وإنشاء الرسومات البيانية فور رفع الملف
            </Typography>
          </Box>
        )}

        {excelData && (
          <>
            <Box id="analysis-section" sx={{ mb: 4 }}>
              <QuickAnalysisPanel
                data={excelData}
                columns={columns}
                autoAnalysis={autoAnalysis}
              />
            </Box>

            <Box sx={{ mb: 4 }}>
              <DataViewer
                data={excelData}
                columns={columns}
                onDataFilter={handleDataFilter}
              />
            </Box>

            <Box sx={{ mb: 4 }}>
              <ChartDashboard
                data={filteredData || excelData}
                columns={columns}
                autoAnalysis={autoAnalysis}
              />
            </Box>

            <Box sx={{ mb: 4 }}>
              <StatisticsPanel
                data={filteredData || excelData}
                columns={columns}
              />
            </Box>
          </>
        )}
      </Container>
    </ThemeProvider>
  );
}

export default App;
