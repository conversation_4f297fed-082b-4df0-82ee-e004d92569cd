/** EVENTS that should be exposed via code generation for the framework components.  */
export declare const _PUBLIC_EVENTS: readonly ["columnEverythingChanged", "newColumnsLoaded", "columnPivotModeChanged", "pivotMaxColumnsExceeded", "columnRowGroupChanged", "expandOrCollapseAll", "columnPivotChanged", "gridColumnsChanged", "columnValueChanged", "columnMoved", "columnVisible", "columnPinned", "columnGroupOpened", "columnResized", "displayedColumnsChanged", "virtualColumnsChanged", "columnHeaderMouseOver", "columnHeaderMouseLeave", "columnHeaderClicked", "columnHeaderContextMenu", "asyncTransactionsFlushed", "rowGroupOpened", "rowDataUpdated", "pinnedRowDataChanged", "pinnedRowsChanged", "rangeSelectionChanged", "cellSelectionChanged", "chartCreated", "chartRangeSelectionChanged", "chartOptionsChanged", "chartDestroyed", "toolPanelVisibleChanged", "toolPanelSizeChanged", "modelUpdated", "cutStart", "cutEnd", "pasteStart", "pasteEnd", "fillStart", "fillEnd", "cellSelectionDeleteStart", "cellSelectionDeleteEnd", "rangeDeleteStart", "rangeDeleteEnd", "undoStarted", "undoEnded", "redoStarted", "redoEnded", "cellClicked", "cellDoubleClicked", "cellMouseDown", "cellContextMenu", "cellValueChanged", "cellEditRequest", "rowValueChanged", "headerFocused", "cellFocused", "rowSelected", "selectionChanged", "tooltipShow", "tooltipHide", "cellKeyDown", "cellMouseOver", "cellMouseOut", "filterChanged", "filterModified", "filterUiChanged", "filterOpened", "floatingFilterUiChanged", "advancedFilterBuilderVisibleChanged", "sortChanged", "virtualRowRemoved", "rowClicked", "rowDoubleClicked", "gridReady", "gridPreDestroyed", "gridSizeChanged", "viewportChanged", "firstDataRendered", "dragStarted", "dragStopped", "dragCancelled", "rowEditingStarted", "rowEditingStopped", "cellEditingStarted", "cellEditingStopped", "bodyScroll", "bodyScrollEnd", "paginationChanged", "componentStateChanged", "storeRefreshed", "stateUpdated", "columnMenuVisibleChanged", "contextMenuVisibleChanged", "rowDragEnter", "rowDragMove", "rowDragLeave", "rowDragEnd", "rowDragCancel", "findChanged", "rowResizeStarted", "rowResizeEnded", "columnsReset"];
/** Exclude the following internal events from code generation to prevent exposing these events via framework components */
export declare const _INTERNAL_EVENTS: readonly ["scrollbarWidthChanged", "keyShortcutChangedCellStart", "keyShortcutChangedCellEnd", "pinnedHeightChanged", "cellFocusCleared", "fullWidthRowFocused", "checkboxChanged", "heightScaleChanged", "suppressMovableColumns", "suppressMenuHide", "suppressFieldDotNotation", "columnPanelItemDragStart", "columnPanelItemDragEnd", "bodyHeightChanged", "columnContainerWidthChanged", "displayedColumnsWidthChanged", "scrollVisibilityChanged", "scrollGapChanged", "columnHoverChanged", "flashCells", "paginationPixelOffsetChanged", "displayedRowsChanged", "leftPinnedWidthChanged", "rightPinnedWidthChanged", "rowContainerHeightChanged", "headerHeightChanged", "columnGroupHeaderHeightChanged", "columnHeaderHeightChanged", "gridStylesChanged", "storeUpdated", "filterDestroyed", "rowDataUpdateStarted", "rowCountReady", "advancedFilterEnabledChanged", "dataTypesInferred", "fieldValueChanged", "fieldPickerValueSelected", "richSelectListRowSelected", "sideBarUpdated", "alignedGridScroll", "alignedGridColumn", "gridOptionsChanged", "chartTitleEdit", "recalculateRowBounds", "stickyTopOffsetChanged", "overlayExclusiveChanged", "beforeRefreshModel", "rowNodeDataChanged", "cellEditValuesChanged", "filterSwitched", "batchEditingStarted", "batchEditingStopped", "filterClosed"];
export declare const _GET_ALL_EVENTS: () => readonly ["columnEverythingChanged", "newColumnsLoaded", "columnPivotModeChanged", "pivotMaxColumnsExceeded", "columnRowGroupChanged", "expandOrCollapseAll", "columnPivotChanged", "gridColumnsChanged", "columnValueChanged", "columnMoved", "columnVisible", "columnPinned", "columnGroupOpened", "columnResized", "displayedColumnsChanged", "virtualColumnsChanged", "columnHeaderMouseOver", "columnHeaderMouseLeave", "columnHeaderClicked", "columnHeaderContextMenu", "asyncTransactionsFlushed", "rowGroupOpened", "rowDataUpdated", "pinnedRowDataChanged", "pinnedRowsChanged", "rangeSelectionChanged", "cellSelectionChanged", "chartCreated", "chartRangeSelectionChanged", "chartOptionsChanged", "chartDestroyed", "toolPanelVisibleChanged", "toolPanelSizeChanged", "modelUpdated", "cutStart", "cutEnd", "pasteStart", "pasteEnd", "fillStart", "fillEnd", "cellSelectionDeleteStart", "cellSelectionDeleteEnd", "rangeDeleteStart", "rangeDeleteEnd", "undoStarted", "undoEnded", "redoStarted", "redoEnded", "cellClicked", "cellDoubleClicked", "cellMouseDown", "cellContextMenu", "cellValueChanged", "cellEditRequest", "rowValueChanged", "headerFocused", "cellFocused", "rowSelected", "selectionChanged", "tooltipShow", "tooltipHide", "cellKeyDown", "cellMouseOver", "cellMouseOut", "filterChanged", "filterModified", "filterUiChanged", "filterOpened", "floatingFilterUiChanged", "advancedFilterBuilderVisibleChanged", "sortChanged", "virtualRowRemoved", "rowClicked", "rowDoubleClicked", "gridReady", "gridPreDestroyed", "gridSizeChanged", "viewportChanged", "firstDataRendered", "dragStarted", "dragStopped", "dragCancelled", "rowEditingStarted", "rowEditingStopped", "cellEditingStarted", "cellEditingStopped", "bodyScroll", "bodyScrollEnd", "paginationChanged", "componentStateChanged", "storeRefreshed", "stateUpdated", "columnMenuVisibleChanged", "contextMenuVisibleChanged", "rowDragEnter", "rowDragMove", "rowDragLeave", "rowDragEnd", "rowDragCancel", "findChanged", "rowResizeStarted", "rowResizeEnded", "columnsReset", "scrollbarWidthChanged", "keyShortcutChangedCellStart", "keyShortcutChangedCellEnd", "pinnedHeightChanged", "cellFocusCleared", "fullWidthRowFocused", "checkboxChanged", "heightScaleChanged", "suppressMovableColumns", "suppressMenuHide", "suppressFieldDotNotation", "columnPanelItemDragStart", "columnPanelItemDragEnd", "bodyHeightChanged", "columnContainerWidthChanged", "displayedColumnsWidthChanged", "scrollVisibilityChanged", "scrollGapChanged", "columnHoverChanged", "flashCells", "paginationPixelOffsetChanged", "displayedRowsChanged", "leftPinnedWidthChanged", "rightPinnedWidthChanged", "rowContainerHeightChanged", "headerHeightChanged", "columnGroupHeaderHeightChanged", "columnHeaderHeightChanged", "gridStylesChanged", "storeUpdated", "filterDestroyed", "rowDataUpdateStarted", "rowCountReady", "advancedFilterEnabledChanged", "dataTypesInferred", "fieldValueChanged", "fieldPickerValueSelected", "richSelectListRowSelected", "sideBarUpdated", "alignedGridScroll", "alignedGridColumn", "gridOptionsChanged", "chartTitleEdit", "recalculateRowBounds", "stickyTopOffsetChanged", "overlayExclusiveChanged", "beforeRefreshModel", "rowNodeDataChanged", "cellEditValuesChanged", "filterSwitched", "batchEditingStarted", "batchEditingStopped", "filterClosed"];
export type AgPublicEventType = (typeof _PUBLIC_EVENTS)[number];
export type AgInternalEventType = (typeof _INTERNAL_EVENTS)[number];
export type AgEventType = AgPublicEventType | AgInternalEventType;
