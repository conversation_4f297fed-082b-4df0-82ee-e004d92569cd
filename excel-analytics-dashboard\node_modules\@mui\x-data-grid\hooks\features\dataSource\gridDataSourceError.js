"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GridUpdateRowError = exports.GridGetRowsError = void 0;
class GridGetRowsError extends Error {
  constructor(options) {
    super(options.message);
    /**
     * The parameters used in the failed request
     */
    this.params = void 0;
    /**
     * The original error that caused this error
     */
    this.cause = void 0;
    this.name = 'GridGetRowsError';
    this.params = options.params;
    this.cause = options.cause;
  }
}
exports.GridGetRowsError = GridGetRowsError;
class GridUpdateRowError extends Error {
  constructor(options) {
    super(options.message);
    /**
     * The parameters used in the failed request
     */
    this.params = void 0;
    /**
     * The original error that caused this error
     */
    this.cause = void 0;
    this.name = 'GridUpdateRowError';
    this.params = options.params;
    this.cause = options.cause;
  }
}
exports.GridUpdateRowError = GridUpdateRowError;