import type { BeanCollection } from '../../context/context';
import type { GetCellEditorInstancesParams, ICellEditor, ICellEditorValidationError } from '../../interfaces/iCellEditor';
import type { EditMap, EditValue } from '../../interfaces/iEditModelService';
import type { EditPosition } from '../../interfaces/iEditService';
import type { CellCtrl } from '../../rendering/cell/cellCtrl';
import { EditRowValidationModel } from '../editModelService';
export declare const UNEDITED: unique symbol;
export declare function getCellEditorInstanceMap<TData = any>(beans: BeanCollection, params?: GetCellEditorInstancesParams<TData>): {
    ctrl: CellCtrl;
    editor: ICellEditor;
}[];
export declare const getCellEditorInstances: <TData = any>(beans: BeanCollection, params?: GetCellEditorInstancesParams<TData>) => ICellEditor[];
export declare function _setupEditors(beans: BeanCollection, editingCells: Required<EditPosition>[], position?: Required<EditPosition>, key?: string | null, event?: Event | null, cellStartedEdit?: boolean | null): void;
export declare function _valuesDiffer({ newValue, oldValue }: Pick<EditValue, 'newValue' | 'oldValue'>): boolean;
export declare function _setupEditor(beans: BeanCollection, position: Required<EditPosition>, params?: {
    key?: string | null;
    event?: Event | null;
    cellStartedEdit?: boolean | null;
    silent?: boolean;
}): void;
export declare function _purgeUnchangedEdits(beans: BeanCollection, includeEditing?: boolean): void;
export declare function _refreshEditorOnColDefChanged(beans: BeanCollection, cellCtrl: CellCtrl): void;
export declare function _syncFromEditors(beans: BeanCollection, params?: {
    event?: Event | null;
}): void;
export declare function _syncFromEditor(beans: BeanCollection, position: Required<EditPosition>, newValue?: any, _source?: string, params?: {
    event?: Event | null;
}): void;
export declare function _destroyEditors(beans: BeanCollection, edits?: Required<EditPosition>[], params?: {
    event?: Event;
    silent?: boolean;
}): void;
export declare function _destroyEditor(beans: BeanCollection, position: Required<EditPosition>, params?: {
    event?: Event | null;
    silent?: boolean;
}): void;
export type MappedValidationErrors = EditMap | undefined;
export declare function _populateModelValidationErrors(beans: BeanCollection): void;
export declare const _generateRowValidationErrors: (beans: BeanCollection) => EditRowValidationModel;
export declare function _validateEdit(beans: BeanCollection): ICellEditorValidationError[] | null;
