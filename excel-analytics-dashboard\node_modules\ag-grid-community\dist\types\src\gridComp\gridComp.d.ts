import type { FocusableContainer } from '../interfaces/iFocusableContainer';
import { TabGuardComp } from '../widgets/tabGuardComp';
export declare class GridComp extends TabGuardComp {
    private readonly gridBody;
    private readonly sideBar;
    private readonly pagination;
    private readonly rootWrapperBody;
    private eGridDiv;
    constructor(eGridDiv: HTMLElement);
    postConstruct(): void;
    private insertGridIntoDom;
    private updateLayoutClasses;
    private createTemplate;
    getFocusableElement(): HTMLElement;
    forceFocusOutOfContainer(up?: boolean): void;
    protected getFocusableContainers(): FocusableContainer[];
}
