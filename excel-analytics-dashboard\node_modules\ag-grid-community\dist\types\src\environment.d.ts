import type { NamedBean } from './context/bean';
import { BeanStub } from './context/beanStub';
import type { BeanCollection } from './context/context';
export declare class Environment extends BeanStub implements NamedBean {
    beanName: "environment";
    private eGridDiv;
    eStyleContainer: HTMLElement;
    cssLayer: string | undefined;
    styleNonce: string | undefined;
    private mutationObserver;
    wireBeans(beans: BeanCollection): void;
    private sizeEls;
    private lastKnownValues;
    private eMeasurementContainer;
    sizesMeasured: boolean;
    private paramsClass;
    private gridTheme;
    private eParamsStyle;
    private globalCSS;
    postConstruct(): void;
    getPinnedRowBorderWidth(): number;
    getRowBorderWidth(): number;
    getDefaultRowHeight(): number;
    getDefaultHeaderHeight(): number;
    getDefaultCellHorizontalPadding(): number;
    private getCellPaddingLeft;
    getCellPadding(): number;
    getDefaultColumnMinWidth(): number;
    getDefaultListItemHeight(): number;
    applyThemeClasses(el: HTMLElement): void;
    refreshRowHeightVariable(): number;
    addGlobalCSS(css: string, debugId: string): void;
    private getCSSVariablePixelValue;
    private measureSizeEl;
    private getMeasurementContainer;
    private getSizeEl;
    private fireGridStylesChangedEvent;
    private refreshRowBorderWidthVariable;
    private handleThemeGridOptionChange;
}
