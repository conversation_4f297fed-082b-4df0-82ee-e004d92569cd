import type { NamedBean } from '../context/bean';
import { BeanStub } from '../context/beanStub';
import type { BeanCollection } from '../context/context';
import type { AgColumn } from '../entities/agColumn';
import type { RowNode } from '../entities/rowNode';
import type { FilterChangedEventSourceType } from '../events';
import type { AdvancedFilterModel } from '../interfaces/advancedFilterModel';
import type { ColumnFilterState, FilterModel, IFilter } from '../interfaces/iFilter';
export declare class FilterManager extends BeanStub implements NamedBean {
    beanName: "filterManager";
    private quickFilter?;
    private advancedFilter;
    private colFilter?;
    wireBeans(beans: BeanCollection): void;
    private externalFilterPresent;
    private aggFiltering;
    private advFilterModelUpdateQueue;
    private alwaysPassFilter?;
    postConstruct(): void;
    private isExternalFilterPresentCallback;
    private doesExternalFilterPass;
    setFilterState(model: FilterModel | null, state: ColumnFilterState | null, source?: FilterChangedEventSourceType): void;
    setFilterModel(model: FilterModel | null, source?: FilterChangedEventSourceType, skipWarning?: boolean): void;
    getFilterModel(): FilterModel;
    getFilterState(): ColumnFilterState | undefined;
    isColumnFilterPresent(): boolean;
    isAggregateFilterPresent(): boolean;
    isChildFilterPresent(): boolean;
    isAnyFilterPresent(): boolean;
    private isAdvFilterPresent;
    private onAdvFilterEnabledChanged;
    isAdvFilterEnabled(): boolean;
    isAdvFilterHeaderActive(): boolean;
    private refreshFiltersForAggregations;
    onFilterChanged(params?: {
        source?: FilterChangedEventSourceType;
        additionalEventAttributes?: any;
        column?: AgColumn;
        columns?: AgColumn[];
    }): void;
    isSuppressFlashingCellsBecauseFiltering(): boolean;
    private isQuickFilterPresent;
    private updateAggFiltering;
    isAggregateQuickFilterPresent(): boolean;
    private isNonAggregateQuickFilterPresent;
    private shouldApplyQuickFilterAfterAgg;
    doesRowPassOtherFilters(colIdToSkip: string, rowNode: RowNode): boolean;
    doesRowPassAggregateFilters(params: {
        rowNode: RowNode;
        colIdToSkip?: string;
    }): boolean;
    doesRowPassFilter(params: {
        rowNode: RowNode;
        colIdToSkip?: string;
    }): boolean;
    isFilterAllowed(column: AgColumn): boolean;
    getAdvFilterModel(): AdvancedFilterModel | null;
    setAdvFilterModel(expression: AdvancedFilterModel | null | undefined, source?: FilterChangedEventSourceType): void;
    toggleAdvFilterBuilder(show: boolean, source: 'api' | 'ui'): void;
    private updateAdvFilterColumns;
    hasFloatingFilters(): boolean;
    getColumnFilterInstance<TFilter extends IFilter>(key: string | AgColumn): Promise<TFilter | null | undefined>;
    private warnAdvFilters;
    setupAdvFilterHeaderComp(eCompToInsertBefore: HTMLElement): void;
    getHeaderRowCount(): number;
    getHeaderHeight(): number;
    private processFilterModelUpdateQueue;
    setColumnFilterModel(key: string | AgColumn, model: any): Promise<void>;
}
