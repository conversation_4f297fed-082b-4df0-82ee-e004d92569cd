import type { BeanCollection } from '../context/context';
import type { InfiniteRowModel } from '../infiniteRowModel/infiniteRowModel';
import type { IClientSideRowModel } from '../interfaces/iClientSideRowModel';
import type { IServerSideRowModel } from '../interfaces/iServerSideRowModel';
export declare function _getClientSideRowModel(beans: BeanCollection): IClientSideRowModel | undefined;
export declare function _getInfiniteRowModel(beans: BeanCollection): InfiniteRowModel | undefined;
export declare function _getServerSideRowModel(beans: BeanCollection): IServerSideRowModel | undefined;
