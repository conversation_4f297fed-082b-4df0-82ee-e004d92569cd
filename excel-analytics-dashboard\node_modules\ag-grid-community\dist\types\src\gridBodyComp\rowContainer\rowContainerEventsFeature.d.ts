import { BeanStub } from '../../context/beanStub';
import type { CellCtrl } from '../../rendering/cell/cellCtrl';
import type { RowCtrl } from '../../rendering/row/rowCtrl';
export declare class RowContainerEventsFeature extends BeanStub {
    readonly element: HTMLElement;
    private editSvc?;
    constructor(element: HTMLElement);
    postConstruct(): void;
    private addKeyboardListeners;
    private addMouseListeners;
    private processMouseEvent;
    getControlsForEventTarget(target: EventTarget | null): {
        cellCtrl: CellCtrl | null;
        rowCtrl: RowCtrl | null;
    };
    private processKeyboardEvent;
    private processCellKeyboardEvent;
    private processFullWidthRowKeyboardEvent;
    private doGridOperations;
    private onCtrlAndA;
    private onCtrlAndC;
    private onCtrlAndX;
    private onCtrlAndV;
    private onCtrlAndD;
    private onCtrlAndZ;
    private onCtrlAndY;
}
