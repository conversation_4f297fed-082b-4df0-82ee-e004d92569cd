import type { <PERSON><PERSON><PERSON><PERSON> } from './api/gridApi';
import type { BeanCollection } from './context/context';
import type { CheckboxLocation, DomLayoutType, FillHandleOptions, GetRowIdFunc, GridOptions, GroupSelectionMode, IsRowSelectable, MasterSelectionMode, MultiRowSelectionOptions, RowSelectionMode, RowSelectionOptions, SelectAllMode, SingleRowSelectionOptions } from './entities/gridOptions';
import type { RowNode } from './entities/rowNode';
import type { GridOptionOrDefault } from './gridOptionsDefault';
import type { ExtractParamsFromCallback, ExtractReturnTypeFromCallback, GridOptionsService } from './gridOptionsService';
import type { GetGroupAggFilteringParams, GetGroupIncludeFooterParams } from './interfaces/iCallbackParams';
import type { IClientSideRowModel } from './interfaces/iClientSideRowModel';
import type { AgG<PERSON><PERSON>om<PERSON>, WithoutGridCommon } from './interfaces/iCommon';
import type { IRowModel } from './interfaces/iRowModel';
import type { IRowNode } from './interfaces/iRowNode';
import type { IServerSideRowModel } from './interfaces/iServerSideRowModel';
export declare function _isClientSideRowModel(gos: GridOptionsService, rowModel?: IRowModel): rowModel is IClientSideRowModel;
export declare function _isServerSideRowModel(gos: GridOptionsService, rowModel?: IRowModel): rowModel is IServerSideRowModel;
export declare function _isDomLayout(gos: GridOptionsService, domLayout: DomLayoutType): boolean;
export declare function _isRowSelection(gos: GridOptionsService): boolean;
export declare function _isGetRowHeightFunction(gos: GridOptionsService): boolean;
export declare function _shouldMaintainColumnOrder(gos: GridOptionsService, isPivotColumns: boolean): boolean;
export declare function _getRowHeightForNode(beans: BeanCollection, rowNode: IRowNode, allowEstimate?: boolean, defaultRowHeight?: number): {
    height: number;
    estimated: boolean;
};
export declare function _getRowHeightAsNumber(beans: BeanCollection): number;
export declare function _getDomData(gos: GridOptionsService, element: Node | null, key: string): any;
export declare function _setDomData(gos: GridOptionsService, element: Element, key: string, value: any): any;
export declare function _getDocument(beans: BeanCollection): Document;
export declare function _getWindow(beans: BeanCollection): Window & typeof globalThis;
export declare function _getRootNode(beans: BeanCollection): Document | ShadowRoot;
export declare function _getActiveDomElement(beans: BeanCollection): Element | null;
export declare function _getPageBody(beans: BeanCollection): HTMLElement | ShadowRoot;
export declare function _anchorElementToMouseMoveEvent(element: HTMLElement, mouseMoveEvent: MouseEvent | Touch, beans: BeanCollection): void;
export declare function _isNothingFocused(beans: BeanCollection): boolean;
export declare function _isAnimateRows(gos: GridOptionsService): NonNullable<boolean | undefined>;
export declare function _isGroupRowsSticky(gos: GridOptionsService): boolean;
export declare function _isColumnsSortingCoupledToGroup(gos: GridOptionsService): boolean;
export declare function _getGroupAggFiltering(gos: GridOptionsService): ((params: WithoutGridCommon<GetGroupAggFilteringParams>) => boolean) | undefined;
export declare function _getGrandTotalRow(gos: GridOptionsService): GridOptions['grandTotalRow'];
export declare function _getGroupTotalRowCallback(gos: GridOptionsService): (params: WithoutGridCommon<GetGroupIncludeFooterParams>) => 'top' | 'bottom' | undefined;
export declare function _isGroupMultiAutoColumn(gos: GridOptionsService): boolean;
export declare function _isGroupUseEntireRow(gos: GridOptionsService, pivotMode: boolean): boolean;
export declare function _isFullWidthGroupRow(gos: GridOptionsService, node: RowNode, pivotMode: boolean): boolean;
export declare function _getRowIdCallback<TData = any>(gos: GridOptionsService): ((params: WithoutGridCommon<ExtractParamsFromCallback<GetRowIdFunc<TData>>>) => ExtractReturnTypeFromCallback<GetRowIdFunc<TData>>) | undefined;
export declare function _canSkipShowingRowGroup(gos: GridOptionsService, node: RowNode): boolean;
export declare function _getMaxConcurrentDatasourceRequests(gos: GridOptionsService): number | undefined;
/** Get the selection checkbox configuration. Defaults to enabled. */
export declare function _shouldUpdateColVisibilityAfterGroup(gos: GridOptionsService, isGrouped: boolean): boolean;
/** Get the selection checkbox configuration. Defaults to enabled. */
export declare function _getCheckboxes(selection: RowSelectionOptions): NonNullable<SingleRowSelectionOptions['checkboxes']> | NonNullable<MultiRowSelectionOptions['checkboxes']>;
/** Get the header checkbox configuration. Defaults to enabled in `multiRow`, otherwise disabled. */
export declare function _getHeaderCheckbox(selection: RowSelectionOptions): boolean;
export declare function _getCheckboxLocation(rowSelection: GridOptions['rowSelection']): CheckboxLocation | undefined;
/** Get the display configuration for disabled checkboxes. Defaults to displaying disabled checkboxes. */
export declare function _getHideDisabledCheckboxes(selection: RowSelectionOptions): boolean;
export declare function _isUsingNewRowSelectionAPI(gos: GridOptionsService): boolean;
export declare function _isUsingNewCellSelectionAPI(gos: GridOptionsService): boolean;
export declare function _getSuppressMultiRanges(gos: GridOptionsService): boolean;
export declare function _isCellSelectionEnabled(gos: GridOptionsService): boolean;
export declare function _getFillHandle(gos: GridOptionsService): FillHandleOptions | undefined;
export declare function _getEnableSelection(gos: GridOptionsService): boolean;
export declare function _getEnableDeselection(gos: GridOptionsService): boolean;
export declare function _getIsRowSelectable(gos: GridOptionsService): IsRowSelectable | undefined;
export declare function _getRowSelectionMode(gridOptions: GridOptions): RowSelectionMode | undefined;
export declare function _getRowSelectionMode(gos: GridOptionsService): RowSelectionMode | undefined;
export declare function _isMultiRowSelection(gridOptions: GridOptions): boolean;
export declare function _isMultiRowSelection(gos: GridOptionsService): boolean;
export declare function _getEnableSelectionWithoutKeys(gos: GridOptionsService): boolean;
export declare function _getGroupSelection(gos: GridOptionsService): GroupSelectionMode | undefined;
export declare function _getSelectAll(gos: GridOptionsService, defaultValue: boolean): SelectAllMode | undefined;
export declare function _getSelectAll(gos: GridOptionsService): SelectAllMode;
export declare function _getGroupSelectsDescendants(gos: GridOptionsService): boolean;
export declare function _getMasterSelects(gos: GridOptionsService): MasterSelectionMode;
export declare function _isSetFilterByDefault(gos: GridOptionsService): boolean;
export declare function _isLegacyMenuEnabled(gos: GridOptionsService): boolean;
export declare function _isColumnMenuAnchoringEnabled(gos: GridOptionsService): boolean;
export declare function _areAdditionalColumnMenuItemsEnabled(gos: GridOptionsService): boolean;
export declare function _getCallbackForEvent(eventName: string): string;
/** Combines component props / attributes with the provided gridOptions returning a new combined gridOptions object */
export declare function _combineAttributesAndGridOptions(gridOptions: GridOptions | undefined, component: any, gridOptionsKeys: string[]): GridOptions;
export declare function _processOnChange(changes: any, api: GridApi): void;
export declare function _addGridCommonParams<T extends AgGridCommon<TData, TContext>, TData = any, TContext = any>(gos: GridOptionsService, params: WithoutGridCommon<T>): T;
export type GroupingApproach = 'group' | 'treeSelfRef' | 'treeNested' | 'treePath';
export declare function _getGroupingApproach(gos: GridOptionsService): GroupingApproach;
/** Used for before GridOptionsService is initialised */
export declare function _getGridOption<K extends keyof GridOptions>(providedGridOptions: GridOptions, gridOption: K): GridOptionOrDefault<K>;
