import type { Theme } from '../../Theme';
import type { CoreParams } from '../../core/core-css';
import type { ColorValue } from '../../theme-types';
import type { BatchEditStyleParams } from '../batch-edit/batch-edit-styles';
import type { ButtonStyleParams } from '../button-style/button-styles';
import type { CheckboxStyleParams } from '../checkbox-style/checkbox-styles';
import type { InputStyleParams } from '../input-style/input-styles';
import type { TabStyleParams } from '../tab-style/tab-styles';
export type ThemeDefaultParams = CoreParams & ButtonStyleParams & CheckboxStyleParams & TabStyleParams & InputStyleParams & BatchEditStyleParams;
export declare const themeQuartz: Theme<ThemeDefaultParams>;
export declare const themeAlpine: Theme<ThemeDefaultParams>;
export declare const themeBalham: Theme<ThemeDefaultParams>;
export type StyleMaterialParams = {
    primaryColor: ColorValue;
};
export declare const styleMaterial: import("../../Part").Part<{
    primaryColor: ColorValue;
}>;
export declare const themeMaterial: Theme<ThemeDefaultParams & StyleMaterialParams>;
