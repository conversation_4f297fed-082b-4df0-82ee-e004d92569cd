import type { BeanCollection } from '../../context/context';
import type { EditPosition } from '../../interfaces/iEditService';
export declare function _hasEdits(beans: BeanCollection, position: EditPosition, includeEditing?: boolean): boolean | undefined;
export declare function _hasLeafEdits(beans: BeanCollection, position: EditPosition): boolean | undefined;
export declare function _hasPinnedEdits(beans: BeanCollection, { rowNode, column }: EditPosition): boolean | undefined;
