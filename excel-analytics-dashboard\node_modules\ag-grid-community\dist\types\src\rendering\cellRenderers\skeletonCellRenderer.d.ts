import type { ILoadingCellRendererComp, ILoadingCellRendererParams } from '../../interfaces/iLoadingCellRenderer';
import { Component } from '../../widgets/component';
export declare class SkeletonCellRenderer extends Component implements ILoadingCellRendererComp {
    constructor();
    init(params: ILoadingCellRendererParams): void;
    private setupFailed;
    private setupLoading;
    refresh(_params: ILoadingCellRendererParams): boolean;
}
