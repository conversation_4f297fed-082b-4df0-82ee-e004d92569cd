import type { BeanCollection } from '../../context/context';
import { Component } from '../../widgets/component';
import type { CellCtrl } from './cellCtrl';
export declare class CellComp extends Component {
    readonly cellCtrl: CellCtrl;
    private eCell;
    private eCellWrapper;
    private eCellValue;
    private cellCssManager;
    private readonly column;
    private readonly rowNode;
    private eRow;
    private includeSelection;
    private includeRowDrag;
    private includeDndSource;
    private forceWrapper;
    private checkboxSelectionComp;
    private dndSourceComp;
    private rowDraggingComp;
    private hideEditorPopup;
    private cellEditorPopupWrapper;
    private cellEditor;
    private cellEditorGui;
    private cellRenderer;
    private cellRendererGui;
    private cellRendererClass;
    private firstRender;
    private rendererVersion;
    private editorVersion;
    constructor(beans: BeanCollection, cellCtrl: CellCtrl, printLayout: boolean, eRow: HTMLElement, editingCell: boolean);
    private getParentOfValue;
    private setRenderDetails;
    private setEditDetails;
    private removeControls;
    private refreshWrapper;
    private addControls;
    private createCellEditorInstance;
    private insertValueWithoutCellRenderer;
    private destroyRenderer;
    private destroyEditor;
    private refreshCellRenderer;
    private createCellRendererInstance;
    private afterCellRendererCreated;
    private afterCellEditorCreated;
    refreshEditStyles(editing: boolean, isPopup?: boolean): void;
    private addInCellEditor;
    private addPopupCellEditor;
    detach(): void;
    destroy(): void;
}
