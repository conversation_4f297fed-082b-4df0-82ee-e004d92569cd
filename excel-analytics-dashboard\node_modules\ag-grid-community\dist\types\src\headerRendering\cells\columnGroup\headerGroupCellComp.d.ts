import { AbstractHeaderCellComp } from '../abstractCell/abstractHeaderCellComp';
import type { HeaderGroupCellCtrl } from './headerGroupCellCtrl';
export declare class HeaderGroupCellComp extends AbstractHeaderCellComp<HeaderGroupCellCtrl> {
    private eResize;
    private readonly eHeaderCompWrapper;
    private headerGroupComp;
    constructor(ctrl: HeaderGroupCellCtrl);
    postConstruct(): void;
    private setUserCompDetails;
    private afterHeaderCompCreated;
    private addOrRemoveHeaderWrapperStyle;
}
