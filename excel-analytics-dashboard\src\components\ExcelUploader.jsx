import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import * as XLSX from 'xlsx';
import {
  Paper,
  Typography,
  Box,
  Button,
  LinearProgress,
  Alert,
  Chip,
  Stack
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Description as DescriptionIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';

const ExcelUploader = ({ onDataUpload }) => {
  const [loading, setLoading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [error, setError] = useState(null);

  const processExcelFile = useCallback((file) => {
    setLoading(true);
    setError(null);
    
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        
        // Get the first worksheet
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        
        // Convert to JSON with header row
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
          header: 1,
          defval: '',
          raw: false
        });
        
        if (jsonData.length === 0) {
          throw new Error('الملف فارغ أو لا يحتوي على بيانات');
        }
        
        // Extract headers and data
        const headers = jsonData[0];
        const rows = jsonData.slice(1);
        
        // Create column definitions
        const columns = headers.map((header, index) => ({
          field: `col_${index}`,
          headerName: header || `العمود ${index + 1}`,
          width: 150,
          editable: false,
          sortable: true,
          filterable: true,
        }));
        
        // Transform data to object format
        const transformedData = rows.map((row, rowIndex) => {
          const rowData = { id: rowIndex };
          headers.forEach((header, colIndex) => {
            rowData[`col_${colIndex}`] = row[colIndex] || '';
          });
          return rowData;
        });
        
        setUploadedFile(file);
        onDataUpload(transformedData, columns);
        setLoading(false);
        
      } catch (err) {
        setError(`خطأ في معالجة الملف: ${err.message}`);
        setLoading(false);
      }
    };
    
    reader.onerror = () => {
      setError('خطأ في قراءة الملف');
      setLoading(false);
    };
    
    reader.readAsArrayBuffer(file);
  }, [onDataUpload]);

  const onDrop = useCallback((acceptedFiles) => {
    const file = acceptedFiles[0];
    if (file) {
      processExcelFile(file);
    }
  }, [processExcelFile]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv']
    },
    multiple: false
  });

  return (
    <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
      <Typography variant="h5" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>
        📁 رفع ملف Excel
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <Box
        {...getRootProps()}
        sx={{
          border: '2px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.300',
          borderRadius: 2,
          p: 4,
          textAlign: 'center',
          cursor: 'pointer',
          bgcolor: isDragActive ? 'action.hover' : 'background.paper',
          transition: 'all 0.3s ease',
          '&:hover': {
            borderColor: 'primary.main',
            bgcolor: 'action.hover'
          }
        }}
      >
        <input {...getInputProps()} />
        
        <CloudUploadIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
        
        <Typography variant="h6" gutterBottom>
          {isDragActive ? 'اسقط الملف هنا...' : 'اسحب وأسقط ملف Excel هنا'}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          أو انقر لاختيار ملف (.xlsx, .xls, .csv)
        </Typography>
        
        <Button
          variant="contained"
          startIcon={<CloudUploadIcon />}
          sx={{ mt: 2 }}
        >
          اختيار ملف
        </Button>
      </Box>
      
      {loading && (
        <Box sx={{ mt: 3 }}>
          <Typography variant="body2" gutterBottom>
            جاري معالجة الملف...
          </Typography>
          <LinearProgress />
        </Box>
      )}
      
      {uploadedFile && !loading && (
        <Box sx={{ mt: 3 }}>
          <Stack direction="row" spacing={2} alignItems="center">
            <CheckCircleIcon color="success" />
            <Typography variant="body1">
              تم رفع الملف بنجاح:
            </Typography>
            <Chip
              icon={<DescriptionIcon />}
              label={uploadedFile.name}
              color="success"
              variant="outlined"
            />
          </Stack>
        </Box>
      )}
    </Paper>
  );
};

export default ExcelUploader;
