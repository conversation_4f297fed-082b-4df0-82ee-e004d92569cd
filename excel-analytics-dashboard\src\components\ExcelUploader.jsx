import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import * as XLSX from 'xlsx';
import <PERSON> from 'papapar<PERSON>';
import {
  Paper,
  Typography,
  Box,
  Button,
  LinearProgress,
  Alert,
  Chip,
  Stack
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Description as DescriptionIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';

const ExcelUploader = ({ onDataUpload }) => {
  const [loading, setLoading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [error, setError] = useState(null);

  const processFile = useCallback((file) => {
    setLoading(true);
    setError(null);

    console.log('بدء معالجة الملف:', file.name, 'الحجم:', file.size, 'النوع:', file.type);

    // Check if it's a CSV file
    if (file.name.toLowerCase().endsWith('.csv') || file.type === 'text/csv') {
      processCSVFile(file);
      return;
    }

    // Process Excel file
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        console.log('تم قراءة الملف، الحجم:', e.target.result.byteLength);

        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });

        console.log('أوراق العمل المتاحة:', workbook.SheetNames);

        // Get the first worksheet
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        console.log('ورقة العمل المحددة:', firstSheetName);
        console.log('نطاق البيانات:', worksheet['!ref']);

        // Convert to JSON with header row
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1,
          defval: '',
          raw: false,
          blankrows: false
        });

        console.log('البيانات المحولة:', jsonData.length, 'صف');
        console.log('أول 3 صفوف:', jsonData.slice(0, 3));

        if (jsonData.length === 0) {
          throw new Error('الملف فارغ أو لا يحتوي على بيانات');
        }

        if (jsonData.length === 1) {
          throw new Error('الملف يحتوي على رؤوس الأعمدة فقط، لا توجد بيانات');
        }

        // Extract headers and data
        const headers = jsonData[0];
        const rows = jsonData.slice(1).filter(row => row && row.length > 0);

        console.log('الرؤوس:', headers);
        console.log('عدد صفوف البيانات:', rows.length);

        if (rows.length === 0) {
          throw new Error('لا توجد بيانات صالحة في الملف');
        }

        // Create column definitions
        const columns = headers.map((header, index) => ({
          field: `col_${index}`,
          headerName: header || `العمود ${index + 1}`,
          width: 150,
          editable: false,
          sortable: true,
          filterable: true,
        }));

        // Transform data to object format
        const transformedData = rows.map((row, rowIndex) => {
          const rowData = { id: rowIndex };
          headers.forEach((header, colIndex) => {
            rowData[`col_${colIndex}`] = row[colIndex] !== undefined ? row[colIndex] : '';
          });
          return rowData;
        });

        console.log('البيانات المحولة النهائية:', transformedData.length, 'صف');
        console.log('عينة من البيانات:', transformedData.slice(0, 2));

        setUploadedFile(file);
        onDataUpload(transformedData, columns);
        setLoading(false);

      } catch (err) {
        console.error('خطأ في معالجة الملف:', err);
        setError(`خطأ في معالجة الملف: ${err.message}`);
        setLoading(false);
      }
    };

    reader.onerror = (err) => {
      console.error('خطأ في قراءة الملف:', err);
      setError('خطأ في قراءة الملف');
      setLoading(false);
    };

    reader.readAsArrayBuffer(file);
  }, [onDataUpload]);

  const processCSVFile = useCallback((file) => {
    console.log('معالجة ملف CSV:', file.name);

    Papa.parse(file, {
      header: false,
      skipEmptyLines: true,
      encoding: 'UTF-8',
      complete: (results) => {
        try {
          console.log('نتائج CSV:', results);

          if (results.errors && results.errors.length > 0) {
            console.warn('تحذيرات CSV:', results.errors);
          }

          const csvData = results.data;

          if (csvData.length === 0) {
            throw new Error('ملف CSV فارغ');
          }

          if (csvData.length === 1) {
            throw new Error('ملف CSV يحتوي على رؤوس الأعمدة فقط');
          }

          // Extract headers and data
          const headers = csvData[0];
          const rows = csvData.slice(1).filter(row => row && row.length > 0);

          console.log('رؤوس CSV:', headers);
          console.log('صفوف CSV:', rows.length);

          // Create column definitions
          const columns = headers.map((header, index) => ({
            field: `col_${index}`,
            headerName: header || `العمود ${index + 1}`,
            width: 150,
            editable: false,
            sortable: true,
            filterable: true,
          }));

          // Transform data to object format
          const transformedData = rows.map((row, rowIndex) => {
            const rowData = { id: rowIndex };
            headers.forEach((header, colIndex) => {
              rowData[`col_${colIndex}`] = row[colIndex] !== undefined ? row[colIndex] : '';
            });
            return rowData;
          });

          console.log('البيانات المحولة من CSV:', transformedData.length, 'صف');

          setUploadedFile(file);
          onDataUpload(transformedData, columns);
          setLoading(false);

        } catch (err) {
          console.error('خطأ في معالجة CSV:', err);
          setError(`خطأ في معالجة ملف CSV: ${err.message}`);
          setLoading(false);
        }
      },
      error: (err) => {
        console.error('خطأ في قراءة CSV:', err);
        setError(`خطأ في قراءة ملف CSV: ${err.message}`);
        setLoading(false);
      }
    });
  }, [onDataUpload]);

  const onDrop = useCallback((acceptedFiles) => {
    const file = acceptedFiles[0];
    if (file) {
      processFile(file);
    }
  }, [processFile]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv'],
      'application/csv': ['.csv'],
      'text/plain': ['.csv']
    },
    multiple: false,
    maxSize: 50 * 1024 * 1024, // 50MB
    onDropRejected: (fileRejections) => {
      const rejection = fileRejections[0];
      if (rejection.errors[0].code === 'file-too-large') {
        setError('الملف كبير جداً. الحد الأقصى 50 ميجابايت');
      } else if (rejection.errors[0].code === 'file-invalid-type') {
        setError('نوع الملف غير مدعوم. يرجى استخدام ملفات .xlsx أو .xls أو .csv');
      } else {
        setError('خطأ في رفع الملف: ' + rejection.errors[0].message);
      }
    }
  });

  return (
    <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
      <Typography variant="h5" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>
        📁 رفع ملف Excel
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <Box
        {...getRootProps()}
        sx={{
          border: '2px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.300',
          borderRadius: 2,
          p: 4,
          textAlign: 'center',
          cursor: 'pointer',
          bgcolor: isDragActive ? 'action.hover' : 'background.paper',
          transition: 'all 0.3s ease',
          '&:hover': {
            borderColor: 'primary.main',
            bgcolor: 'action.hover'
          }
        }}
      >
        <input {...getInputProps()} />
        
        <CloudUploadIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
        
        <Typography variant="h6" gutterBottom>
          {isDragActive ? 'اسقط الملف هنا...' : 'اسحب وأسقط ملف Excel هنا'}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          أو انقر لاختيار ملف (.xlsx, .xls, .csv)
        </Typography>
        
        <Button
          variant="contained"
          startIcon={<CloudUploadIcon />}
          sx={{ mt: 2 }}
        >
          اختيار ملف
        </Button>
      </Box>
      
      {loading && (
        <Box sx={{ mt: 3 }}>
          <Typography variant="body2" gutterBottom>
            جاري معالجة الملف... يرجى الانتظار
          </Typography>
          <LinearProgress />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            تتم معالجة البيانات وتحويلها للعرض. قد يستغرق هذا بضع ثوانٍ للملفات الكبيرة.
          </Typography>
        </Box>
      )}
      
      {uploadedFile && !loading && (
        <Box sx={{ mt: 3 }}>
          <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
            <CheckCircleIcon color="success" />
            <Typography variant="body1">
              تم رفع الملف بنجاح:
            </Typography>
            <Chip
              icon={<DescriptionIcon />}
              label={uploadedFile.name}
              color="success"
              variant="outlined"
            />
          </Stack>
          <Stack direction="row" spacing={2}>
            <Chip
              label={`الحجم: ${(uploadedFile.size / 1024).toFixed(1)} KB`}
              size="small"
              variant="outlined"
            />
            <Chip
              label={`النوع: ${uploadedFile.type || 'غير محدد'}`}
              size="small"
              variant="outlined"
            />
            <Chip
              label="جاهز للتحليل"
              size="small"
              color="success"
              variant="filled"
            />
          </Stack>
        </Box>
      )}
    </Paper>
  );
};

export default ExcelUploader;
