import type { AgEvent } from '../events';
import type { FilterAction } from '../interfaces/iFilter';
import { Component } from '../widgets/component';
export interface FilterButtonCompParams {
    className?: string;
}
export interface FilterButtonEvent extends AgEvent<FilterAction> {
    event?: Event;
}
export declare class FilterButtonComp extends Component<FilterAction> {
    private buttons;
    private listeners;
    private eApply?;
    private readonly className;
    constructor(config?: FilterButtonCompParams);
    updateButtons(buttons: FilterAction[], useForm?: boolean): void;
    updateValidity(valid?: boolean): void;
    private destroyListeners;
    destroy(): void;
}
