import type { _Column<PERSON>ilter<PERSON>rid<PERSON><PERSON>, _Filter<PERSON>rid<PERSON><PERSON>, _QuickFilterGridApi } from '../api/gridApi';
import type { _ModuleWithApi } from '../interfaces/iModule';
import type { _ModuleWithoutApi } from '../interfaces/iModule';
/**
 * @internal
 */
export declare const ClientSideRowModelFilterModule: _ModuleWithoutApi;
/**
 * @internal
 */
export declare const FilterCoreModule: _ModuleWithApi<_FilterGridApi>;
/**
 * @internal
 */
export declare const FilterValueModule: _ModuleWithoutApi;
/**
 * @internal
 */
export declare const ColumnFilterModule: _ModuleWithApi<_ColumnFilterGridApi>;
/**
 * @feature Filtering -> Custom Column Filters
 */
export declare const CustomFilterModule: _ModuleWithoutApi;
/**
 * @feature Filtering -> Text Filter
 */
export declare const TextFilterModule: _ModuleWithoutApi;
/**
 * @feature Filtering -> Number Filter
 */
export declare const NumberFilterModule: _ModuleWithoutApi;
/**
 * @feature Filtering -> Date Filter
 */
export declare const DateFilterModule: _ModuleWithoutApi;
/**
 * @feature Filtering -> Quick Filter
 * @gridOption quickFilterText
 */
export declare const QuickFilterModule: _ModuleWithApi<_QuickFilterGridApi>;
/**
 * @feature Filtering -> External Filter
 * @gridOption doesExternalFilterPass
 */
export declare const ExternalFilterModule: _ModuleWithoutApi;
