import type { BeanCollection } from '../context/context';
import type { ColDef, ColGroupDef, HeaderLocation } from '../entities/colDef';
import type { Column, ColumnPinnedType } from '../interfaces/iColumn';
import type { ApplyColumnStateParams, ColumnState } from './columnStateUtils';
export type ColumnChangedEventType = 'columnValueChanged' | 'columnPivotChanged' | 'columnRowGroupChanged';
export declare function getColumnDef<TValue = any, TData = any>(beans: BeanCollection, key: string | Column<TValue>): ColDef<TData, TValue> | null;
export declare function getColumnDefs<TData = any>(beans: BeanCollection): (ColDef<TData> | ColGroupDef<TData>)[] | undefined;
export declare function getDisplayNameForColumn(beans: BeanCollection, column: Column, location: HeaderLocation): string;
export declare function getColumn<TValue = any, TData = any>(beans: BeanCollection, key: string | ColDef<TData, TValue> | Column<TValue>): Column<TValue> | null;
export declare function getColumns(beans: BeanCollection): Column[] | null;
export declare function applyColumnState(beans: BeanCollection, params: ApplyColumnStateParams): boolean;
export declare function getColumnState(beans: BeanCollection): ColumnState[];
export declare function resetColumnState(beans: BeanCollection): void;
export declare function isPinning(beans: BeanCollection): boolean;
export declare function isPinningLeft(beans: BeanCollection): boolean;
export declare function isPinningRight(beans: BeanCollection): boolean;
export declare function getDisplayedColAfter(beans: BeanCollection, col: Column): Column | null;
export declare function getDisplayedColBefore(beans: BeanCollection, col: Column): Column | null;
export declare function setColumnsVisible(beans: BeanCollection, keys: (string | Column)[], visible: boolean): void;
export declare function setColumnsPinned(beans: BeanCollection, keys: (string | ColDef | Column)[], pinned: ColumnPinnedType): void;
export declare function getAllGridColumns(beans: BeanCollection): Column[];
export declare function getDisplayedLeftColumns(beans: BeanCollection): Column[];
export declare function getDisplayedCenterColumns(beans: BeanCollection): Column[];
export declare function getDisplayedRightColumns(beans: BeanCollection): Column[];
export declare function getAllDisplayedColumns(beans: BeanCollection): Column[];
export declare function getAllDisplayedVirtualColumns(beans: BeanCollection): Column[];
