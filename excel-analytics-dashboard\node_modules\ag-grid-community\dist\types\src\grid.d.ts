import type { <PERSON>rid<PERSON><PERSON> } from './api/gridApi';
import { Context } from './context/context';
import type { GridOptions } from './entities/gridOptions';
import type { IFrameworkOverrides } from './interfaces/iFrameworkOverrides';
import type { Module } from './interfaces/iModule';
export interface GridParams {
    globalListener?: (...args: any[]) => any;
    globalSyncListener?: (...args: any[]) => any;
    frameworkOverrides?: IFrameworkOverrides;
    providedBeanInstances?: {
        [key: string]: any;
    };
    setThemeOnGridDiv?: boolean;
    /**
     * Modules to be registered directly with this grid instance.
     */
    modules?: Module[];
}
export interface Params {
    /**
     * Modules to be registered directly with this grid instance.
     */
    modules?: Module[];
}
/**
 * Creates a grid inside the provided HTML element.
 * @param eGridDiv Parent element to contain the grid.
 * @param gridOptions Configuration for the grid.
 * @param params Individually register AG Grid Modules to this grid.
 * @returns api to be used to interact with the grid.
 */
export declare function createGrid<TData>(eGridDiv: HTMLElement, gridOptions: GridOptions<TData>, params?: Params): GridApi<TData>;
export declare class GridCoreCreator {
    create(eGridDiv: HTMLElement, providedOptions: GridOptions, createUi: (context: Context) => void, acceptChanges?: (context: Context) => void, params?: GridParams, destroyCallback?: () => void): GridApi;
    private getRegisteredModules;
    private registerModuleFeatures;
    private createProvidedBeans;
    private createBeansList;
}
