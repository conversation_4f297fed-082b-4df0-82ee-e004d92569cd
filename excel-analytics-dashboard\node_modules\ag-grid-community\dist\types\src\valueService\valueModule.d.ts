import type { _Value<PERSON><PERSON>, _ValueCacheApi } from '../api/gridApi';
import type { _ModuleWithApi, _ModuleWithoutApi } from '../interfaces/iModule';
/**
 * @feature Performance -> Value Cache
 * @gridOption valueCache
 */
export declare const ValueCacheModule: _ModuleWithApi<_ValueCacheApi>;
/**
 * @feature Cells -> Expression
 */
export declare const ExpressionModule: _ModuleWithoutApi;
/**
 * @feature Change Detection
 * @gridOption suppressChangeDetection
 */
export declare const ChangeDetectionModule: _ModuleWithoutApi;
/**
 * @feature Cells -> API
 */
export declare const CellApiModule: _ModuleWithApi<_ValueApi<any>>;
