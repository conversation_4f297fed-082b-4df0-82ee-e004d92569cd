import type { Environment } from '../environment';
export declare const IS_SSR: boolean;
export declare const _injectGlobalCSS: (css: string, styleContainer: HTMLElement, debugId: string, layer: string | undefined, priority: number, nonce: string | undefined) => void;
export declare const _injectCoreAndModuleCSS: (styleContainer: HTMLElement, layer: string | undefined, nonce: string | undefined) => void;
export declare const _registerGridUsingThemingAPI: (environment: Environment) => void;
export declare const _unregisterGridUsingThemingAPI: (environment: Environment) => void;
