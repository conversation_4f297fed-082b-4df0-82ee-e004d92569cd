var GridEditModes = /*#__PURE__*/function (GridEditModes) {
  GridEditModes["Cell"] = "cell";
  GridEditModes["Row"] = "row";
  return GridEditModes;
}(GridEditModes || {});
var GridCellModes = /*#__PURE__*/function (GridCellModes) {
  GridCellModes["Edit"] = "edit";
  GridCellModes["View"] = "view";
  return GridCellModes;
}(GridCellModes || {});
var GridRowModes = /*#__PURE__*/function (GridRowModes) {
  GridRowModes["Edit"] = "edit";
  GridRowModes["View"] = "view";
  return GridRowModes;
}(GridRowModes || {});
export { GridEditModes, GridCellModes, GridRowModes };