import type { RowNode } from '../entities/rowNode';
import type { IPinnedRowModel } from '../interfaces/iPinnedRowModel';
import type { RowPinnedType } from '../interfaces/iRowNode';
/** Whether the given node is pinned manually to the top or bottom container */
export declare function _isManualPinnedRow(rowNode: RowNode): boolean;
export declare function _getNodesInRangeForSelection(rowModel: IPinnedRowModel, float: NonNullable<RowPinnedType>, start: RowNode | undefined, end: RowNode | undefined): RowNode[];
