import type { Part } from '../../Part';
import type { BorderValue, ColorValue, DurationValue, LengthValue } from '../../theme-types';
export type TabStyleParams = {
    /**
     * Background color of tabs
     */
    tabBackgroundColor: ColorValue;
    /**
     * Background color of the container for tabs
     */
    tabBarBackgroundColor: ColorValue;
    /**
     * Border below the container for tabs
     */
    tabBarBorder: BorderValue;
    /**
     * Padding at the left and right of the container for tabs
     */
    tabBarHorizontalPadding: LengthValue;
    /**
     * Padding at the top of the container for tabs
     */
    tabBarTopPadding: LengthValue;
    /**
     * Padding at the bottom of the container for tabs
     */
    tabBottomPadding: LengthValue;
    /**
     * Padding inside the top and bottom sides of the container for tabs
     */
    tabHorizontalPadding: LengthValue;
    /**
     * Background color of tabs when hovered over
     */
    tabHoverBackgroundColor: ColorValue;
    /**
     * Color of text within tabs when hovered over
     */
    tabHoverTextColor: ColorValue;
    /**
     * Background color of selected tabs
     */
    tabSelectedBackgroundColor: ColorValue;
    /**
     * Color of the border around selected tabs
     */
    tabSelectedBorderColor: ColorValue;
    /**
     * Width of the border around selected tabs
     */
    tabSelectedBorderWidth: LengthValue;
    /**
     * Color of text within the selected tabs
     */
    tabSelectedTextColor: ColorValue;
    /**
     * Color of line drawn under selected tabs
     */
    tabSelectedUnderlineColor: ColorValue;
    /**
     * Duration in seconds of the fade in/out transition for the line drawn under selected tabs
     */
    tabSelectedUnderlineTransitionDuration: DurationValue;
    /**
     * Width of line drawn under selected tabs
     */
    tabSelectedUnderlineWidth: LengthValue;
    /**
     * Spacing between tabs
     */
    tabSpacing: LengthValue;
    /**
     * Color of text within tabs
     */
    tabTextColor: ColorValue;
    /**
     * Padding at the top of the container for tabs
     */
    tabTopPadding: LengthValue;
};
/**
 * This base tab style adds no visual styling, it provides a base upon which a
 * tab style can be built by setting the tab-related params
 */
export declare const tabStyleBase: Part<TabStyleParams>;
/**
 * Tabs styled for the Quartz theme
 */
export declare const tabStyleQuartz: Part<TabStyleParams>;
/**
 * Tabs styled for the Material theme
 */
export declare const tabStyleMaterial: Part<TabStyleParams>;
/**
 * Tabs styled for the Alpine theme
 */
export declare const tabStyleAlpine: Part<TabStyleParams>;
/**
 * Tabs where the selected tab appears raised and attached the the active
 * content, like a rolodex or operating system tabs.
 */
export declare const tabStyleRolodex: Part<TabStyleParams>;
