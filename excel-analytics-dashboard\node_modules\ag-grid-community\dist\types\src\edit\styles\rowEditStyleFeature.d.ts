import { BeanStub } from '../../context/beanStub';
import type { BeanCollection } from '../../context/context';
import type { IRowStyleFeature } from '../../interfaces/iRowStyleFeature';
import type { RowCtrl } from '../../rendering/row/rowCtrl';
export declare class RowEditStyleFeature extends BeanStub implements IRowStyleFeature {
    private readonly rowCtrl;
    private editSvc?;
    private editModelSvc?;
    constructor(rowCtrl: RowCtrl, beans: BeanCollection);
    applyRowStyles(): void;
    private applyStyle;
}
